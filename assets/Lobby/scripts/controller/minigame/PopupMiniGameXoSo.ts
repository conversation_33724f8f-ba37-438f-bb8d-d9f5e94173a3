import { _decorator, Node, Label, Toggle, instantiate, EditBox, Color, Sprite, RichText } from 'cc';
import Dialog from "db://assets/Lobby/scripts/common/Dialog";
import App from "db://assets/Lobby/scripts/common/App";
import Http from "db://assets/Lobby/scripts/common/Http";
import Configs from "db://assets/Lobby/scripts/common/Config";
import {Utils} from "db://assets/Lobby/scripts/common/Utils";
import {BroadcastReceiver} from "db://assets/Lobby/scripts/common/BroadcastListener";
import {LanguageManager} from "db://assets/Lobby/scripts/common/language/Language.LanguageManager";

const {ccclass, property} = _decorator;

@ccclass('PopupMiniGameXoSo')
export default class PopupMiniGameXoSo extends Dialog {

    @property(Label)
    lblGameSession: Label = null;
    @property([Node])
    tabContents: Node[] = [];
    @property([Toggle])
    tabToggles: Toggle[] = [];
    @property([Node])
    tabContentsPickNumber: Node[] = [];
    @property([Toggle])
    tabTogglesPickNumber: Toggle[] = [];
    @property([Node])
    tabContentsAchievement: Node[] = [];
    @property([Toggle])
    tabTogglesAchievement: Toggle[] = [];
    @property(Node)
    containerBoxCalenderResults: Node = null;

    private currentTabIndex: number = 0;
    private currentTabIndexPickNumber: number = 0;
    private currentTabIndexAchievement: number = 0;
    private isShowContainerBoxCalenderResult: boolean = false;
    private selectedLocationID: number = 0;
    private selectedLocationName: string = "";
    private selectedDate: string = "";

    // tab 0
    @property(Label)
    titleTab0: Label = null;
    @property(Label)
    titleTab1: Label = null;
    @property(Toggle)
    dropdownToggle: Toggle = null;
    @property(Node)
    dropdownList: Node = null;
    @property(Node)
    dropdownItem: Node = null;
    @property(Label)
    labelSelectedLocationName: Label = null;
    @property(Label)
    labelTimeLeft: Label = null;

    private betTypes = [];
    private betHelpTextTypes = {
        1: "xs23",
        2: "xs8",
        3: "xs11",
    };
    private betNameTypes = {
        1: 'xs14',
        2: 'xs15',
        3: 'xs16',
        4: 'xs18',
        5: 'xs20',
        6: 'xs39',
        7: 'xs10',
        8: 'xs22',
        9: 'xs24',
        10: 'xs14',
        11: 'xs15',
        12: 'xs16',
        13: 'xs18',
        14: 'xs20',
        15: 'xs33',
        16: 'xs39',
        17: 'xs35',
        18: 'xs34',
        19: 'xs36',
        20: 'xs37',
        21: 'xs38',
        22: 'xs14',
        23: 'xs15',
        24: 'xs16',
        25: 'xs18',
        26: 'xs20',
        27: 'xs33',
        28: 'xs39',
        29: 'xs35',
        30: 'xs34',
        31: 'xs36',
        32: 'xs37',
        33: 'xs38'
    };

    @property(Node)
    containerListLo: Node = null;
    @property(Node)
    containerListXien: Node = null;
    @property(Node)
    containerListDe: Node = null;
    @property(Node)
    containerListDauDuoi: Node = null;
    @property(Node)
    containerItemTemplate: Node = null;

    private betTypeSelected: number = -1;
    private betDataSelected: string = "";
    private betValueSelected: string = "";
    private isUseTicket = false;
    private allowBet: boolean = false;

    // popup
    @property(Node)
    containerPopup: Node = null;
    @property(Label)
    labelPopupTitle: Label = null;
    @property(Label)
    labelPopupBetData: Label = null;
    @property(Label)
    labelPopupBetValue: Label = null;
    @property(Label)
    labelPopupBetTotalValue: Label = null;
    @property(Label)
    labelPopupBetTotalWin: Label = null;
    @property(Node)
    popupFastChoiceAmount: Node = null;

    // tab result
    @property(Color)
    selectedColor: Color = new Color(255, 100, 100);
    @property(Color)
    normalColor: Color = new Color().fromHEX("#cdbee4");
    @property([Node])
    rows: Node[] = [];
    @property(Label)
    labelTitleTime: Label = null;
    @property(Label)
    labelTitleTimeBox: Label = null;
    @property(Node)
    calendarContainer: Node = null;
    @property(Node)
    dayItemPrefab: Node = null;
    @property(Node)
    gridCalendar: Node = null;
    private currentDay: number = new Date().getDate();
    private currentDate: Date = new Date();
    private selectedDay: number = new Date().getDate();
    private selectedNode: Node = null;
    @property(Label)
    labelTabResultTab: Label = null;
    @property(Toggle)
    dropdownToggleTabResult: Toggle = null;
    @property(Node)
    dropdownListTabResult: Node = null;
    @property(Label)
    labelSelectedLocationNameTabResult: Label = null;
    private selectedAreaIDTabResult: number = 0;
    private selectedLocationIDTabResult: number = 0;

    @property(Node)
    leftNotResult: Node = null;
    @property(Node)
    leftContainer27: Node = null;
    @property(Node)
    leftContainer18: Node = null;

    @property([Label])
    firsts: Label[] = [];
    @property([Label])
    lasts: Label[] = [];

    @property([Label])
    result0_27: Label[] = [];
    @property([Label])
    result1_27: Label[] = [];
    @property([Label])
    result2_27: Label[] = [];
    @property([Label])
    result3_27: Label[] = [];
    @property([Label])
    result4_27: Label[] = [];
    @property([Label])
    result5_27: Label[] = [];
    @property([Label])
    result6_27: Label[] = [];
    @property([Label])
    result7_27: Label[] = [];

    @property([Label])
    result0_18: Label[] = [];
    @property([Label])
    result1_18: Label[] = [];
    @property([Label])
    result2_18: Label[] = [];
    @property([Label])
    result3_18: Label[] = [];
    @property([Label])
    result4_18: Label[] = [];
    @property([Label])
    result5_18: Label[] = [];
    @property([Label])
    result6_18: Label[] = [];
    @property([Label])
    result7_18: Label[] = [];
    @property([Label])
    result8_18: Label[] = [];

    // tab history
    @property(Node)
    containerListHistory: Node = null;
    @property(Node)
    containerItemHistory: Node = null;
    @property(Toggle)
    dropdownToggleTabHistory: Toggle = null;
    @property(Toggle)
    dropdownToggleStatusTabHistory: Toggle = null;
    @property(Node)
    dropdownListTabHistory: Node = null;
    @property(Node)
    dropdownItemTabHistory: Node = null;
    @property(Label)
    labelSelectedLocationNameTabHistory: Label = null;
    private checkAllStatus: boolean = true;
    private dataTabHistory = [];

    // tab Achievement
    @property(Node)
    containerListAchievement: Node = null;
    @property(Node)
    containerItemTop1Achievement: Node = null;
    @property(Node)
    containerItemTop2Achievement: Node = null;
    @property(Node)
    containerItemTop3Achievement: Node = null;
    @property(Node)
    containerItemAchievement: Node = null;

    // ticket
    @property(Label)
    labelTicket1K: Label = null;
    @property(Label)
    labelTicket2K: Label = null;
    @property(Label)
    labelTicket5K: Label = null;

    private ticketBalance1K: number = 0;
    private ticketBalance2K: number = 0;
    private ticketBalance5K: number = 0;

    start() {
        this.showTab(0);
        this.generateCalendar();

        let today = new Date();
        let dayStr = today.getDate().toString().padStart(2, "0");
        let monthStr = (today.getMonth() + 1).toString().padStart(2, "0");
        let yearStr = today.getFullYear().toString();

        this.labelTitleTime.string = `${dayStr}/${monthStr}/${yearStr}`;
        this.labelTitleTimeBox.string = `${dayStr}/${monthStr}/${yearStr}`;

        this.updateDataDropdownTabResult();

        this.currentDay = today.getDate();

        this.labelTicket1K.string = "0";
        this.labelTicket2K.string = "0";
        this.labelTicket5K.string = "0";
        this.checkTicket();
    }

    checkTicket() {
        App.instance.showLoading(true);
        Http.get(Configs.App.DOMAIN_CONFIG['GetAccountTicket'], { CurrencyID: Configs.Login.CurrencyID, GameID: 115 }, (status, res) => {
            if (status === 200) {

                this.ticketBalance1K = 0;
                this.ticketBalance2K = 0;
                this.ticketBalance5K = 0;

                res.d.filter((item: any) => item.gameID === 115).forEach((item: any) => {
                    if (item.roomID === 1) {
                        this.ticketBalance1K += item.balance;
                    } else if (item.roomID === 2) {
                        this.ticketBalance2K += item.balance;
                    } else if (item.roomID === 3) {
                        this.ticketBalance5K += item.balance;
                    }
                });

                this.labelTicket1K.string = this.ticketBalance1K.toString();
                this.labelTicket2K.string = this.ticketBalance2K.toString();
                this.labelTicket5K.string = this.ticketBalance5K.toString();
            }
            App.instance.showLoading(false);
        });
    }

    generateCalendar() {
        if (!this.dayItemPrefab || this.rows.length < 5) {
            return;
        }

        this.rows.forEach(row => row.removeAllChildren());

        let year = this.currentDate.getFullYear();
        let month = this.currentDate.getMonth();
        let totalDays = new Date(year, month + 1, 0).getDate();

        let rowIndex = 0;
        this.selectedNode = null;

        for (let i = 1; i <= totalDays; i++) {
            let dayItem = instantiate(this.dayItemPrefab);
            let label = dayItem.getChildByName("text").getComponent(Label);

            if (!label) {
                return;
            }

            label.string = i.toString();

            if (i === this.selectedDay) {
                dayItem.getComponent(Sprite).color = this.selectedColor;
                this.selectedNode = dayItem;
            } else {
                dayItem.getComponent(Sprite).color = this.normalColor;
            }

            dayItem.on(Node.EventType.TOUCH_END, () => this.onDaySelected(dayItem, i), this);

            this.rows[rowIndex].addChild(dayItem);

            if ((i % 7 === 0) && rowIndex < this.rows.length - 1) {
                rowIndex++;
            }
        }
    }

    onDaySelected(dayNode: Node, day: number) {
        if (this.selectedNode) {
            this.selectedNode.getComponent(Sprite).color = this.normalColor;
        }

        this.selectedDay = day;
        this.selectedNode = dayNode;
        this.selectedNode.getComponent(Sprite).color = this.selectedColor;

        let dayStr = day.toString().padStart(2, "0");
        let monthStr = (this.currentDate.getMonth() + 1).toString().padStart(2, "0");
        let yearStr = this.currentDate.getFullYear().toString();

        let currentDate = `${dayStr}/${monthStr}/${yearStr}`;

        this.labelTitleTime.string = currentDate;
        this.labelTitleTimeBox.string = currentDate;
        this.titleTab1.string = App.instance.getTextLang('xs2') + ' - ' + `${dayStr}/${monthStr}/${yearStr}`;
        this.containerBoxCalenderResults.active = false;
        this.isShowContainerBoxCalenderResult = !this.isShowContainerBoxCalenderResult;
        this.updateDataDropdownTabResult();
    }

    updateDataDropdownTabResult() {
        App.instance.showLoading(true);
        Http.get(Configs.App.DOMAIN_CONFIG['LotteryLocationByDateUrl'], { date: this.labelTitleTime.string.replace(/\//g, '%2F') }, (status, res) => {
            if (status === 200) {
                this.dropdownListTabResult.removeAllChildren();

                res.d.forEach((item: any, index: any) => {
                    var itemResult = instantiate(this.dropdownItem);
                    itemResult.getChildByName('label').getComponent(Label).string = item.LocationName;
                    itemResult.on(Node.EventType.TOUCH_END, () => {
                        this.dropdownToggleTabResult.isChecked = false;
                        this.selectedAreaIDTabResult = item.AreaID;
                        this.selectedLocationIDTabResult = item.LocationID;
                        this.labelSelectedLocationNameTabResult.string = item.LocationName;

                        this.updateDataTabResult();
                    });

                    if (index === 0) {
                        this.selectedAreaIDTabResult = item.AreaID;
                        this.selectedLocationIDTabResult = item.LocationID;
                        this.labelSelectedLocationNameTabResult.string = item.LocationName;
                        this.updateDataTabResult();
                    }

                    this.dropdownListTabResult.addChild(itemResult);
                });
            }

            App.instance.showLoading(false);
        });
    }

    onNextMonth() {
        let month = this.currentDate.getMonth();
        let year = this.currentDate.getFullYear();

        if (month === 11) {
            this.currentDate.setFullYear(year + 1);
            this.currentDate.setMonth(0);
        } else {
            this.currentDate.setMonth(month + 1);
        }

        this.updateCalendarAfterMonthChange();
    }

    onPreviousMonth() {
        let month = this.currentDate.getMonth();
        let year = this.currentDate.getFullYear();

        if (month === 0) {
            this.currentDate.setFullYear(year - 1);
            this.currentDate.setMonth(11);
        } else {
            this.currentDate.setMonth(month - 1);
        }

        this.updateCalendarAfterMonthChange();
    }

    updateCalendarAfterMonthChange() {
        let newTotalDays = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() + 1, 0).getDate();
        if (this.selectedDay > newTotalDays) {
            this.selectedDay = newTotalDays;
        }

        this.selectedNode = null;
        this.generateCalendar();

        let dayStr = this.selectedDay.toString().padStart(2, "0");
        let monthStr = (this.currentDate.getMonth() + 1).toString().padStart(2, "0");
        let yearStr = this.currentDate.getFullYear().toString();

        this.labelTitleTime.string = `${dayStr}/${monthStr}/${yearStr}`;
        this.labelTitleTimeBox.string = `${dayStr}/${monthStr}/${yearStr}`;
    }


    selectedToggleTabs(toggle: Toggle, index: string) {
        let tabIndex = parseInt(index);

        if (this.currentTabIndex === tabIndex) {
            return;
        }
        if (toggle.isChecked == false) return;
        this.showTab(tabIndex);
    }

    _updateTimer: Function = null;

    startCountdown(timeLeftInSeconds: number, onFinish?: Function) {
        this.unschedule(this._updateTimer);

        let remaining = timeLeftInSeconds;

        this._updateTimer = () => {
            if (remaining <= 0) {
                this.labelTimeLeft.string = "00:00:00";
                this.unschedule(this._updateTimer);
                if (onFinish) onFinish();
                return;
            }

            const formatTime = (time: number) => String(time).padStart(2, '0');

            const hours = formatTime(Math.floor(remaining / 3600));
            const minutes = formatTime(Math.floor((remaining % 3600) / 60));
            const seconds = formatTime(remaining % 60);

            this.labelTimeLeft.string = `${hours}:${minutes}:${seconds}`;
            remaining -= 1;
        };

        this._updateTimer();
        this.schedule(this._updateTimer, 1);
    }

    showTab(tabIndex: number) {
        this.currentTabIndex = tabIndex;

        this.tabContents.forEach((tab, i) => {
            tab.active = i === tabIndex;
        });

        this.tabToggles.forEach((toggle, i) => {
            this.updateToggleTextColor(toggle, i === tabIndex);
        });

        if (tabIndex === 0) {
            let now = new Date();

            if (now.getHours() >= 19) {
                now.setDate(now.getDate() + 1);
            }

            var currentDate = now.toLocaleDateString('en-GB');

            this.titleTab0.string = App.instance.getTextLang('xs2') + `: ${currentDate.replace(/\//g, '-')}`;
            this.titleTab1.string = App.instance.getTextLang('xs2') + ` - ${currentDate.replace(/\//g, '/')}`;

            App.instance.showLoading(true);
            Http.get(Configs.App.DOMAIN_CONFIG['LotteryLocationByDateUrl'], { date: currentDate.replace(/\//g, '%2F') }, (status, res) => {
                if (status === 200) {
                    this.dropdownList.removeAllChildren();
                    this.dropdownListTabHistory.removeAllChildren();

                    res.d.forEach((item: any, index: number) => {
                        var dropdownItem = instantiate(this.dropdownItem);
                        dropdownItem.getChildByName('label').getComponent(Label).string = item.LocationName;

                        dropdownItem.on(Node.EventType.TOUCH_END, () => {
                            this.onSelectedLocation(item);
                        });

                        if (index === 0) {
                            this.onSelectedLocation(item);
                        }

                        this.dropdownList.addChild(dropdownItem);

                        this.labelSelectedLocationNameTabHistory.string = App.instance.getTextLang('sl13');
                        if (index === 0) {
                            var dropdownItemAllTabHistory = instantiate(this.dropdownItemTabHistory);
                            dropdownItemAllTabHistory.getChildByName('label').getComponent(Label).string = App.instance.getTextLang('sl13');
                            dropdownItemAllTabHistory.on(Node.EventType.TOUCH_END, () => {
                                this.dropdownToggleTabHistory.isChecked = false;
                                this.labelSelectedLocationNameTabHistory.string = App.instance.getTextLang('sl13');

                                this.updateDataTabHistory();
                            });

                            this.dropdownListTabHistory.addChild(dropdownItemAllTabHistory);
                        }

                        var dropdownItemTabHistory = instantiate(this.dropdownItemTabHistory);
                        dropdownItemTabHistory.getChildByName('label').getComponent(Label).string = item.LocationName;
                        dropdownItemTabHistory.on(Node.EventType.TOUCH_END, () => {
                            this.dropdownToggleTabHistory.isChecked = false;
                            this.labelSelectedLocationNameTabHistory.string = item.LocationName;

                            this.updateDataTabHistory();
                        });

                        this.dropdownListTabHistory.addChild(dropdownItemTabHistory);
                    });
                    App.instance.showLoading(false);
                }
            });
        }

        if (tabIndex === 2) {
            Http.get(Configs.App.DOMAIN_CONFIG['LotteryBetOfAccountUrl'], { currencyID : Configs.Login.CurrencyID}, (status, res) => {
                if (status === 200) {
                    this.dataTabHistory = res.d;

                    this.updateDataTabHistory();
                }
            });
        }

        if (tabIndex === 3) {
            this.showTabPickAchievement(0);
        }

        this.tabContents[2].getComponentsInChildren(Toggle).forEach((toggle: Toggle) => {
            toggle.isChecked = false;
        });
    }

    updateDataTabHistory() {
        var data = this.dataTabHistory;
        if (this.labelSelectedLocationNameTabHistory.string !== App.instance.getTextLang('sl13')) {
            data = this.dataTabHistory.filter((item: any) => item.LocationName === this.labelSelectedLocationNameTabHistory.string);
        }

        if (!this.checkAllStatus) {
            data = data.filter((item: any) => item.PrizeValue > 0);
        }

        this.containerListHistory.removeAllChildren();
        data.forEach((item: any) => {
            var itemHistory = instantiate(this.containerItemHistory);
            itemHistory.active = true;
            itemHistory.getChildByName('label1').getComponent(Label).string = Utils.formatDatetime(item.CreatedTime, "dd/MM/yyyy HH:mm:ss");
            itemHistory.getChildByName('label2').getComponent(Label).string = item.LocationName;
            itemHistory.getChildByName('label3').getComponent(Label).string = `#${item.GameSessionID}`;
            itemHistory.getChildByName('label4').getComponent(Label).string = App.instance.getTextLang(this.betNameTypes[item.BetType]) + ": " + item.BetData;
            itemHistory.getChildByName('label5').getComponent(Label).string = Utils.formatNumber(item.BetValue);
            itemHistory.getChildByName('label6').getComponent(Label).string = item.IsAwarded === 0 ? "" : item.PrizeValue;

            this.containerListHistory.addChild(itemHistory);
        });
    }

    onToggleAllStatus(_event: any, customData: string) {
        this.checkAllStatus = customData === "ALL";
        this.dropdownToggleStatusTabHistory.isChecked = false;
        this.updateDataTabHistory();
    }

    updateDataTabResult() {
        var currentDate = this.labelTitleTime.string;
        Http.get(Configs.App.DOMAIN_CONFIG['LotteryGetResultsUrl'], { date: currentDate.replace(/\//g, '%2F'), areaID: this.selectedAreaIDTabResult }, (status, res) => {
            if (status === 200) {
                this.leftNotResult.active = true;
                this.leftContainer27.active = false;
                this.leftContainer18.active = false;

                this.firsts.forEach((item: Label) => item.string = "");
                this.lasts.forEach((item: Label) => item.string = "");

                res.d.forEach((item: any) => {
                    if (item.LocationID != this.selectedLocationIDTabResult) {
                        return;
                    }

                    if (item.Result.length === 0) {
                        this.leftNotResult.active = true;
                        this.leftContainer27.active = false;
                        this.leftContainer18.active = false;
                    } else {
                        if (this.selectedAreaIDTabResult === 1) {
                            this.leftNotResult.active = false;
                            this.leftContainer27.active = true;
                            this.leftContainer18.active = false;

                            item.Result.forEach((item: any) => {
                                item.ResultData.forEach((result: string, index: number) => {
                                    const key = `result${item.Type}_27`;
                                    if (this[key]?.[index]) {
                                        this[key][index].string = result;
                                    }
                                });
                            });
                        } else {
                            this.leftNotResult.active = false;
                            this.leftContainer27.active = false;
                            this.leftContainer18.active = true;

                            item.Result.forEach((item: any) => {
                                item.ResultData.forEach((result: string, index: number) => {
                                    const key = `result${item.Type}_18`;
                                    if (this[key]?.[index]) {
                                        this[key][index].string = result;
                                    }
                                });
                            });
                        }
                    }

                    item.Statistics.forEach((item: any) => {
                        this.firsts[parseInt(item.Num)].string = this.removeConsecutiveDuplicates(item.Before);
                        this.lasts[parseInt(item.Num)].string = this.removeConsecutiveDuplicates(item.After);
                    });
                });
            }

            App.instance.showLoading(false);
        });
    }

    onSelectedLocation(item: any) {
        this.selectedDate = item.Date.substring(0, 10);
        this.selectedLocationID = item.LocationID;
        this.selectedLocationName = item.LocationName;
        this.labelSelectedLocationName.string = item.LocationName;
        this.dropdownToggle.isChecked = false;

        App.instance.showLoading(true);
        Http.get(Configs.App.DOMAIN_CONFIG['LotteryBetTypeUrl'], { areaID: item.AreaID }, (status, res) => {
            App.instance.showLoading(false);
            if (status === 200) {
                this.containerListLo.removeAllChildren();
                this.containerListDe.removeAllChildren();
                this.containerListDauDuoi.removeAllChildren();
                this.containerListXien.removeAllChildren();

                res.d.forEach((item: any) => {
                    this.betTypes[item.BetType] = item;

                    if ([1, 2, 10, 11, 22, 23].includes(item.BetType)) {
                        this.makeBetTypeItem(item.BetType, this.containerListLo);
                    }

                    if ([6, 7, 15, 16, 27, 28].includes(item.BetType)) {
                        this.makeBetTypeItem(item.BetType, this.containerListDe);
                    }

                    if ([8, 9, 17, 18, 19, 20, 21, 29, 30, 31, 32, 33].includes(item.BetType)) {
                        this.makeBetTypeItem(item.BetType, this.containerListDauDuoi);
                    }

                    if ([3, 4, 5, 12, 13, 14, 24, 25, 26].includes(item.BetType)) {
                        this.makeBetTypeItem(item.BetType, this.containerListXien);
                    }
                });

            }
        });

        this.fetchTimeAndStartCountdown(item.LocationID);
    }

    fetchTimeAndStartCountdown(locationID: number) {
        Http.get(Configs.App.DOMAIN_CONFIG['LotteryCurrentDataUrl'], { LocationID: locationID }, (_status, res) => {
            this.lblGameSession.string = `#${res.d.GameSessionID}`;
            let timeRemainBetting = res.d.RemainBetting;
            let timeRemainWaiting = res.d.RemainWaiting;
            this.allowBet = res.d.Status == 1;

            if (timeRemainWaiting === 0 && timeRemainBetting > 0) {
                this.startCountdown(timeRemainBetting, () => {
                    this.fetchTimeAndStartCountdown(locationID);
                });
            } else if (timeRemainBetting === 0 && timeRemainWaiting > 0) {
                this.startCountdown(timeRemainWaiting, () => {
                    this.fetchTimeAndStartCountdown(locationID);
                });
            } else {
                this.scheduleOnce(() => this.fetchTimeAndStartCountdown(locationID), 2);
            }
        });
    }

    makeBetTypeItem(betType: number, container: Node) {
        var item = instantiate(this.containerItemTemplate);
        item.active = true;
        var childNumbers = item.children[0];
        var childAmount = item.children[1];

        var helpText = childNumbers.getChildByName('help_text_number').getComponent(Label);
        if ([3, 12, 24].includes(betType)) {
            helpText.string = App.instance.getTextLang('xs17');
        } else if ([4, 13, 25].includes(betType)) {
            helpText.string = App.instance.getTextLang('xs19');
        } else if ([5, 14, 26].includes(betType)) {
            helpText.string = App.instance.getTextLang('xs21');
        } else {
            helpText.string = App.instance.getTextLang(this.betHelpTextTypes[this.betTypes[betType].Length]);
        }
        childNumbers.getChildByName('header').getComponent(Label).string = App.instance.getTextLang(this.betNameTypes[betType]);
        childAmount.getChildByName('multiple').getComponent(Label).string = "x" + this.betTypes[betType].Multiple;

        var edbNumbers = childNumbers.getComponentInChildren(EditBox);
        var edbBetNode = childAmount.children[2];
        var edbBetLabel = edbBetNode.children[1].getComponent(RichText);

        edbBetNode.on("click", () => {
            if (edbNumbers.string.trim() === "") return;

            var popupFastChoice = instantiate(this.popupFastChoiceAmount);
            popupFastChoice.active = true;
            popupFastChoice.parent = this.popupFastChoiceAmount.parent;
            var fast = popupFastChoice.getChildByName('Fast');
            var other = popupFastChoice.getChildByName('Other');
            var lblCurrentBet = popupFastChoice.getChildByName('Current').getComponent(Label);
            fast.active = true;
            other.active = false;
            lblCurrentBet.string = "";

            var freeTicket = fast.getChildByName('FreeTicket');
            var freeTicket1K = freeTicket.getChildByName('1K');
            var freeTicket2K = freeTicket.getChildByName('2K');
            var freeTicket5K = freeTicket.getChildByName('5K');

            freeTicket1K.active = this.ticketBalance1K > 0;
            freeTicket2K.active = this.ticketBalance2K > 0;
            freeTicket5K.active = this.ticketBalance5K > 0;

            if (this.ticketBalance1K > 0) {
                freeTicket1K.children[1].getComponentInChildren(Label).string = this.ticketBalance1K.toString();
                freeTicket1K.on(Node.EventType.TOUCH_END, () => {
                    this.betWithFreeTicket(edbNumbers, betType, 1000);
                    popupFastChoice.destroy();
                });
            }

            if (this.ticketBalance2K > 0) {
                freeTicket2K.children[1].getComponentInChildren(Label).string = this.ticketBalance2K.toString();
                freeTicket2K.on(Node.EventType.TOUCH_END, () => {
                    this.betWithFreeTicket(edbNumbers, betType, 2000);
                    popupFastChoice.destroy();
                });
            }

            if (this.ticketBalance5K > 0) {
                freeTicket5K.children[1].getComponentInChildren(Label).string = this.ticketBalance5K.toString();
                freeTicket5K.on(Node.EventType.TOUCH_END, () => {
                    this.betWithFreeTicket(edbNumbers, betType, 5000);
                    popupFastChoice.destroy();
                });
            }

            fast.getChildByName('ChoiceNumbers').children.forEach(child => {
                child.on(Node.EventType.TOUCH_END, () => {
                    edbBetLabel.string = Utils.formatNumber(parseInt(child.name));
                    popupFastChoice.destroy();
                });
            })

            fast.getChildByName('Other').on(Node.EventType.TOUCH_END, () => {
                fast.active = false;
                other.active = true;
            });

            fast.getChildByName('Cancel').on(Node.EventType.TOUCH_END, () => {
                edbBetLabel.string = '0';
                popupFastChoice.destroy();
            });

            other.getChildByName('Fast').on(Node.EventType.TOUCH_END, () => {
                fast.active = true;
                other.active = false;
            });

            other.getChildByName('Cancel').on(Node.EventType.TOUCH_END, () => {
                edbBetLabel.string = '0';
                popupFastChoice.destroy();
            });

            other.getChildByName('Confirm').on(Node.EventType.TOUCH_END, () => {
                edbBetLabel.string = Utils.formatNumber(parseInt(lblCurrentBet.string));
                popupFastChoice.destroy();
            });

            other.getChildByName('ChoiceNumbers').children.forEach(child => {
                child.on(Node.EventType.TOUCH_END, () => {
                    if (child.name == 'DEL') {
                        lblCurrentBet.string = lblCurrentBet.string.slice(0, -1);
                    } else {
                        lblCurrentBet.string += child.name;
                    }
                });
            })
        });

        item.getChildByName('btn').on(Node.EventType.TOUCH_END, () => {
            if (!this.allowBet) {
                App.instance.alertDialog.showMsg(App.instance.getTextLang('me-207'));
                return;
            }

            var betData: string;
            var count: number;
            var isInvalid: boolean;
            var edbBetValue = edbBetLabel.string.replace(/\./g, '');

            [isInvalid, betData, count] = this.validateBetData(edbNumbers, betType);

            if (edbBetValue === "") {
                App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_invalid_input'));
                isInvalid = false;
            } else {
                if (parseInt(edbBetValue) < this.betTypes[betType].MinBetValue || parseInt(edbBetValue) > this.betTypes[betType].MaxBetValue) {
                    App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_invalid_input'));
                    isInvalid = false;
                }
            }

            if (!isInvalid) {
                return;
            }

            this.betTypeSelected = betType;
            this.betDataSelected = betData;
            this.betValueSelected = edbBetValue;
            this.isUseTicket = false;

            var totalValue = parseInt(edbBetValue) * count;
            var totalWin = totalValue * this.betTypes[betType].Multiple;

            this.labelPopupTitle.string = App.instance.getTextLang('txt_ban_da_nhap') + " " +
                App.instance.getTextLang(this.betNameTypes[betType]) +
                " - " + this.selectedLocationName + " - " + this.getFormattedDate();
            this.labelPopupBetData.string = this.betDataSelected;
            this.labelPopupBetValue.string = (parseInt(this.betValueSelected)).toLocaleString("vi-VN");
            this.labelPopupBetTotalValue.string = totalValue.toLocaleString("vi-VN");
            this.labelPopupBetTotalWin.string = totalWin.toLocaleString("vi-VN");

            this.containerPopup.active = true;
        });

        container.addChild(item);
    }

    cancelBet() {
        this.containerPopup.active = false;
    }

    actSubmitBet() {
        App.instance.showLoading(true);
        var betData = this.betDataSelected;
        var betValue = this.betValueSelected;
        var betCount = betData.split(',').length;
        if ([3, 4, 5, 12, 13, 14, 24, 25, 26].includes(this.betTypeSelected)) {
            betCount = 1;
        }

        var roomID = 0;
        var useTicket = false;

        if (this.isUseTicket) {
            if (betValue === '1000' && betCount <= this.ticketBalance1K) {
                useTicket = true;
                roomID = 1;
            } else if (betValue === '2000' && betCount <= this.ticketBalance2K) {
                useTicket = true;
                roomID = 2;
            } else if (betValue === '5000' && betCount <= this.ticketBalance5K) {
                useTicket = true;
                roomID = 3;
            }
        }

        var payload = {
            BetData: betData,
            BetValue: betValue,
            BetType: this.betTypeSelected,
            CurrencyID: Configs.Login.CurrencyID,
            Ticket: useTicket,
            RoomID: roomID,
            LocationID: this.selectedLocationID,
        }

        Http.post(Configs.App.DOMAIN_CONFIG['LotterySetBetUrl'], payload, (_status, res) => {
            App.instance.showLoading(false);
            this.resetAllEditBox();
            BroadcastReceiver.send(BroadcastReceiver.USER_UPDATE_COIN);
            this.checkTicket();

            if (res.c < 0) {
                App.instance.alertDialog.showMsg(App.instance.getTextLang(`me${res.c}`));
                return;
            }

            for (var i = 0; i < res.d.Data.length; i++) {
                if (res.d.Data[i].ResponseStatus < 0) {
                    App.instance.alertDialog.showMsg(App.instance.getTextLang(`me${res.d.Data[i].ResponseStatus}`));
                    return;
                }
            }


            App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_success'));
            this.containerPopup.active = false;
        });
    }

    selectedToggleTabsPickNumber(toggle: Toggle, index: string) {
        let tabIndex = parseInt(index);

        if (this.currentTabIndexPickNumber === tabIndex) {
            return;
        }

        this.resetAllEditBox();
        if (toggle.isChecked == false) return;
        this.showTabPickNumber(tabIndex);
    }

    showTabPickNumber(tabIndex: number) {
        this.currentTabIndexPickNumber = tabIndex;

        this.tabContentsPickNumber.forEach((tab, i) => {
            tab.active = i === tabIndex;
        });

        this.tabTogglesPickNumber.forEach((toggle, i) => {
            this.updateToggleTextColor(toggle, i === tabIndex);
        });
    }

    selectedToggleTabsAchievement(toggle: Toggle, index: string) {
        let tabIndex = parseInt(index);

        if (this.currentTabIndexAchievement === tabIndex) {
            return;
        }

        if (toggle.isChecked == false) return;
        this.showTabPickAchievement(tabIndex);
    }

    showTabPickAchievement(tabIndex: number) {
        this.currentTabIndexAchievement = tabIndex;

        App.instance.showLoading(true);
        Http.get(Configs.App.DOMAIN_CONFIG['LotteryTopProfitUrl'], { currencyID: Configs.Login.CurrencyID, type: tabIndex + 1 }, (status, res) => {
            if (status === 200) {
                this.tabContents[3].getChildByName('NoData').active = res.d.length == 0;
                this.containerListAchievement.removeAllChildren();
                res.d.forEach((item: any, index: number) => {
                    var itemAchievement = instantiate(this.containerItemAchievement);

                    if (index === 0) {
                        itemAchievement = instantiate(this.containerItemTop1Achievement);
                    } else if (index === 1) {
                        itemAchievement = instantiate(this.containerItemTop2Achievement);
                    } else if (index === 2) {
                        itemAchievement = instantiate(this.containerItemTop3Achievement);
                    } else {
                        itemAchievement.getChildByName('rank').getComponent(Label).string = (index + 1) + "";
                    }

                    itemAchievement.getChildByName('label2').getComponent(Label).string = item.Nickname;
                    itemAchievement.getChildByName('label3').getChildByName('text').getComponent(Label).string = item.PrizeValue.toLocaleString("vi-VN");

                    this.containerListAchievement.addChild(itemAchievement);
                });
            }

            App.instance.showLoading(false);
        });

        this.tabTogglesAchievement.forEach((toggle, i) => {
            this.updateToggleTextColor(toggle, i === tabIndex);
        });
    }

    private updateToggleTextColor(toggle: Toggle, isActive: boolean) {
        let textNode = toggle.node.getChildByName("text");
        if (textNode) {
            let label = textNode.getComponent(Label);
            if (label) {
                label.color = isActive ? new Color(255, 240, 0) : new Color(255, 255, 255);
            }
        }
    }

    showBoxCalender() {
        this.isShowContainerBoxCalenderResult = !this.isShowContainerBoxCalenderResult;

        this.containerBoxCalenderResults.active = this.isShowContainerBoxCalenderResult;
        this.generateCalendar();
    }

    removeConsecutiveDuplicates(input: string) {
        return input
            .split(", ")
            .filter((num, index, arr) => num !== arr[index - 1])
            .join(", ");
    }

    resetAllEditBox() {
        this.node.getComponentsInChildren(EditBox).forEach((editBox) => {
            editBox.string = "";
        });

        this.node.getComponentsInChildren(RichText).forEach((richText) => {
            richText.string = "0";
        });
    }

    private getFormattedDate() {
        const lang = LanguageManager.instance.locale;
        const now = new Date();
        const day = String(now.getDate()).padStart(2, '0');
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const year = now.getFullYear();
        const weekdayIndex = now.getDay(); // 0 = Sunday

        const weekdays = {
            vi: ["Chủ nhật", "Thứ 2", "Thứ 3", "Thứ 4", "Thứ 5", "Thứ 6", "Thứ 7"],
            en: ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"],
            id: ["Minggu", "Senin", "Selasa", "Rabu", "Kamis", "Jumat", "Sabtu"],
            zh: ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"],
            th: ["วันอาทิตย์", "วันจันทร์", "วันอังคาร", "วันพุธ", "วันพฤหัสบดี", "วันศุกร์", "วันเสาร์"],
            km: ["អាទិត្យ", "ច័ន្ទ", "អង្គារ", "ពុធ", "ព្រហស្បតិ៍", "សុក្រ", "សៅរ៍"]
        };

        const dayWord = {
            vi: "ngày",
            en: "day",
            id: "tanggal",
            zh: "日",
            th: "วันที่",
            km: "ថ្ងៃទី"
        };

        const weekday = weekdays[lang] ? weekdays[lang][weekdayIndex] : weekdays['vi'][weekdayIndex];
        const dayLabel = dayWord[lang] || dayWord['vi'];

        return `${weekday} ${dayLabel} ${day}/${month}/${year}`;
    }


    private validateBetData(edbNumbers: EditBox, betType: number): any {
        var isInvalid = true;
        var betData = edbNumbers.string;
        betData.split(',').forEach((number) => {
            if (number.length !== this.betTypes[betType].Length || isNaN(parseInt(number))) {
                App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_invalid_input'));
                isInvalid = false;
            }
        });

        var count = betData.split(',').length;
        if ([3, 12, 24].includes(betType)) {
            if (count !== 2) {
                App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_invalid_input'));
                isInvalid = false;
            } else {
                count = 1;
            }
        } else if ([4, 13, 25].includes(betType)) {
            if (count !== 3) {
                App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_invalid_input'));
                isInvalid = false;
            } else {
                count = 1;
            }
        } else if ([5, 14, 26].includes(betType)) {
            if (count !== 4) {
                App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_invalid_input'));
                isInvalid = false;
            } else {
                count = 1;
            }
        }

        const arr = betData.split(",");
        const hasDuplicates = new Set(betData.split(',')).size !== arr.length;
        if (hasDuplicates) {
            App.instance.alertDialog.showMsg(App.instance.getTextLang('me-67014'));
            isInvalid = false;
        }

        return [isInvalid, betData, count];
    }

    private betWithFreeTicket(edbNumbers: EditBox, betType: number, ticketAmount: number): void {
        if (!this.allowBet) {
            App.instance.alertDialog.showMsg(App.instance.getTextLang('me-207'));
            return;
        }

        var betData: string;
        var count: number;
        var isInvalid: boolean;

        [isInvalid, betData, count] = this.validateBetData(edbNumbers, betType);

        if (!isInvalid) {
            return;
        }

        if (betType == 1000 && count > this.ticketBalance1K) {
            App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_not_enough_ticket'));
            return;
        }

        if (betType == 2000 && count > this.ticketBalance2K) {
            App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_not_enough_ticket'));
            return;
        }

        if (betType == 5000 && count > this.ticketBalance5K) {
            App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_not_enough_ticket'));
            return;
        }

        this.betTypeSelected = betType;
        this.betDataSelected = betData;
        this.betValueSelected = ticketAmount.toString();
        this.isUseTicket = true;

        var totalValue = ticketAmount * count;
        var totalWin = totalValue * this.betTypes[betType].Multiple;

        this.labelPopupTitle.string = App.instance.getTextLang('txt_ban_da_nhap') + " " +
            App.instance.getTextLang(this.betNameTypes[betType]) +
            " - " + this.selectedLocationName + " - " + this.getFormattedDate();
        this.labelPopupBetData.string = this.betDataSelected;
        this.labelPopupBetValue.string = ticketAmount.toLocaleString("vi-VN") + " (" + App.instance.getTextLang('spr074') + ")";
        this.labelPopupBetTotalValue.string = totalValue.toLocaleString("vi-VN") + " (" + App.instance.getTextLang('spr074') + ")";
        this.labelPopupBetTotalWin.string = totalWin.toLocaleString("vi-VN");

        this.containerPopup.active = true;
    }
}
