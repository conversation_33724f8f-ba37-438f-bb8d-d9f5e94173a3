import { _decorator, Component, Node, Color, Label, Toggle } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('TabEvent')
export default class TabEvent extends Component {
    @property([Node])
    tabs: Node[] = [];

    @property([Node])
    tabContents: Node[] = [];

    @property([Node])
    tabSelectedCheckMark: Node[] = [];

    start() {
        this.showTab(0);
    }

    onTabSelected(toggle: Toggle, index: string) {
        const tabIndex = parseInt(index);
        this.showTab(tabIndex);
    }

    showTab(index: number) {
        this.tabContents.forEach((tab, i) => {
            if (tab) tab.active = (i === index);
        });

        this.tabSelectedCheckMark.forEach((check, i) => {
            if (check) check.active = (i === index);
        });

        // this.tabSelectedCheckMark.forEach((tab, i) => {
        //     if (i !== index) {
        //         tab.active = false;
        //     }
        // });

        this.tabs.forEach((tab, i) => {
            const labelNode = tab.getChildByName("label");
            if (labelNode) {
                const label = labelNode.getComponent(Label);
                if (label) {
                    label.color = (i === index)
                        ? new Color(255, 252, 0)
                        : new Color(255, 255, 255);
                }
            }
        });
    }
}
