import {
    _decorator,
    Component,
    Node,
    Toggle,
    Prefab,
    Sprite,
    SpriteFrame,
    Label,
    instantiate,
    tween,
    easing,
    log,
} from "cc";

import Dialog from "db://assets/Lobby/scripts/common/Dialog";
import {Global} from "db://assets/Lobby/scripts/common/Global";
import App from "db://assets/Lobby/scripts/common/App";
import Configs from "db://assets/Lobby/scripts/common/Config";
import Http from "db://assets/Lobby/scripts/common/Http";
import {Utils} from "db://assets/Lobby/scripts/common/Utils";

const { ccclass, property } = _decorator;

export interface JackpotItem {
    gameID: number;
    gameName: string;
    roomID: number;
    jackpotFund: number;
    multiplier: number;
    nextJackpot: number;
}

export interface GroupedJackpot {
    gameName: string;
    rooms: JackpotItem[];
}

@ccclass("PopupEventX2")
export default class PopupEventX2 extends Dialog {
    @property([Node]) tabToggle: Node[] = [];
    @property([Node]) tabRoomIDSelected: Node[] = [];
    @property([Node]) tabRoomIDSelectedMyLogs: Node[] = [];


    @property(Toggle) tabEvent: Toggle = null;
    @property([Node]) tabs: Node[] = [];
    @property([Node]) tabContents: Node[] = [];
    @property(Prefab) listJackpotPrefab: Prefab = null;
    @property(Node) listJackpotContent: Node = null;
    @property(Prefab) logJackpotPrefab: Prefab = null;
    @property(Node) logJackpotContent: Node = null;
    @property(Node) preButton: Node = null;
    @property(Node) nextButton: Node = null;
    @property(Node) listMyJackpotContent: Node = null;

    @property(SpriteFrame) logo_fortune: SpriteFrame = null;
    @property(SpriteFrame) logo_kingdom: SpriteFrame = null;
    @property(SpriteFrame) logo_ocean: SpriteFrame = null;
    @property(SpriteFrame) logo_oracle: SpriteFrame = null;
    @property(SpriteFrame) logo_dancingNight: SpriteFrame = null;
    @property(SpriteFrame) logo_dancing: SpriteFrame = null;
    @property(SpriteFrame) logo_forest: SpriteFrame = null;
    @property(SpriteFrame) logo_resurrection: SpriteFrame = null;
    @property(SpriteFrame) logo_minipoker: SpriteFrame = null;

    private currentPage = 1;
    private itemsPerPage = 10;
    private jackpotData: any[] = [];
    private activeTabIndex: number = 0;
    private activeRoomTabIndex: number = -1;
    private activeRoomTabMyLogIndex: number = -1;





    start() {
        this.startFetchingJackpot();
    }

    startFetchingJackpot() {
        this.getListJackpot();
        this.schedule(this.getListJackpot, 0.5);
    }

    onTabSelected(toggle: Toggle, index: string) {
        const tabIndex = parseInt(index);

        if (toggle.isChecked === false) return;

        this.showTab(tabIndex);
    }

    onTabRoomIDSelected(toggle: Toggle, index: string) {

        const tabIndex = parseInt(index);

        this.tabRoomIDSelected.forEach((tab, i) => {
            const btn = tab.getChildByName("active");
            if (btn) btn.active = i === tabIndex;
        });

        if (this.activeRoomTabIndex === tabIndex) {
            return;
        }

        this.activeRoomTabIndex = tabIndex;

        // Cập nhật UI tab room
        // this.tabToggle.forEach((tab, i) => {
        //     const btn = tab.getChildByName("active");
        //     if (btn) btn.active = i === tabIndex;
        // });


        this.getLogJackpot(1, parseInt(index), "", false);
    }

    onTabRoomIDSelectedMyLogs(toggle: Toggle, index: string) {

        const tabIndex = parseInt(index);

        this.tabRoomIDSelectedMyLogs.forEach((tab, i) => {
            const btn = tab.getChildByName("active");
            if (btn) btn.active = i === tabIndex;
        });

        if (this.activeRoomTabMyLogIndex === tabIndex) {
            return;
        }

        this.activeRoomTabMyLogIndex = tabIndex;

        this.getLogJackpot(1, parseInt(index), "", true);
    }

    showTab(index: number) {
        this.activeTabIndex = index;

        this.tabToggle.forEach((tab, i) => {
            const btn = tab.getChildByName("btn-room-type");
            if (btn) btn.active = i === index;
        });

        this.tabContents.forEach((tab, i) => tab && (tab.active = i === index));

        this.unschedule(this.getListJackpot);
        switch (index) {
            case 0:
                this.startFetchingJackpot();
                break;
            case 2:
                this.getLogJackpot(1, 1, "", false);
                break;
            case 3:
                this.getLogJackpot(1, 1, "", true);
                break;
        }
    }

    dismiss() {
        super.dismiss();
    }

    _onDismissed() {
        this.node.getComponentsInChildren("EditBox").forEach((edit: any) => {
            edit.tabIndex = -1;
        });
        this.node.active = false;
    }

    getListJackpot() {
        if (Configs.Login.IsLogin) {
            Http.get(Configs.App.DOMAIN_CONFIG["GetListJackpot"], { CurrencyID: Configs.Login.CurrencyID }, (status, res) => {
                if (status === 200 && res.c === 0) {
                    const groupedData = this.groupJackpotData(res.d);
                    this.populateLogList(groupedData);
                }
            });
        }
    }

    getLogJackpot(currency: number, roomID: number, listGame: string, myLogs: boolean) {
        App.instance.showLoading(true);
        if (Configs.Login.IsLogin) {
            Http.get(Configs.App.DOMAIN_CONFIG["GetJackpotEventLogs"], { Currency: currency, RoomID: roomID, ListGames: listGame, MyLogs: myLogs }, (status, res) => {
                if (status === 200 && res.c === 0) {
                    App.instance.showLoading(false);
                    this.populateLogJackpot(res.d, myLogs ? this.listMyJackpotContent : this.logJackpotContent);
                }
            });
        }
    }

    populateLogJackpot(data: any[], container: Node) {
        this.jackpotData = data.sort((a, b) => b.prizeValue - a.prizeValue);
        this.currentPage = 1;
        this.renderPage(container);
    }

    renderPage(container: Node) {
        container.removeAllChildren();
        const start = (this.currentPage - 1) * this.itemsPerPage;
        const end = start + this.itemsPerPage;
        const pageData = this.jackpotData.slice(start, end);

        pageData.forEach((item, index) => {
            const logItem = instantiate(this.logJackpotPrefab);
            logItem.parent = container;

            const bg1 = logItem.getChildByName("bg1");
            const bg2 = logItem.getChildByName("bg2");
            if (bg1 && bg2) {
                bg1.active = index % 2 === 0;
                bg2.active = index % 2 !== 0;
            }

            const content = logItem.getChildByName("content");
            // content.getChildByName("time").getComponent(Label).string = this.formatDate(item.createdTime);
            content.getChildByName("time").getComponent(Label).string = Utils.formatDatetime(item.createdTime, "dd/MM/yyyy HH:mm:ss");
            const teamNode = content.getChildByName("name").getChildByName("teamName").getComponent(Label);
            const userNode = content.getChildByName("name").getChildByName("displayName").getComponent(Label);

            const match = item.nickname.match(/^\[(.*?)\](.*)$/);
            if (match) {
                teamNode.string = `[${match[1]}]`;
                userNode.string = match[2];
            } else {
                teamNode.string = "";
                userNode.string = item.nickname;
            }

            content.getChildByName("coinWin").getComponent(Label).string = item.prizeValue.toLocaleString();
        });

        this.updatePaginationButtons();
    }

    nextPage() {
        const container = this.getActiveContainer();
        if (this.currentPage < Math.ceil(this.jackpotData.length / this.itemsPerPage)) {
            this.currentPage++;
            this.renderPage(container);
        }
    }

    prevPage() {
        const container = this.getActiveContainer();
        if (this.currentPage > 1) {
            this.currentPage--;
            this.renderPage(container);
        }
    }

    getActiveContainer(): Node {
        if (this.activeTabIndex === 2) return this.logJackpotContent;
        if (this.activeTabIndex === 3) return this.listMyJackpotContent;
        return null;
    }

    updatePaginationButtons() {
        const totalPages = Math.ceil(this.jackpotData.length / this.itemsPerPage);
        this.preButton.active = this.currentPage > 1;
        this.nextButton.active = this.currentPage < totalPages;
    }

    populateLogList(data: GroupedJackpot[]) {
        this.listJackpotContent.removeAllChildren();
        const logoMap = {
            213: this.logo_fortune,
            201: this.logo_kingdom,
            207: this.logo_ocean,
            203: this.logo_oracle,
            215: this.logo_dancingNight,
            205: this.logo_dancing,
            211: this.logo_forest,
            107: this.logo_resurrection,
            101: this.logo_minipoker,
        };

        data.forEach(group => {
            const logItem = instantiate(this.listJackpotPrefab);
            logItem.parent = this.listJackpotContent;

            const gameNameLabel = logItem.getChildByName("game").getChildByName("bgListJP").getChildByName("nameGame").getComponent(Label);
            const gameNameNode = logItem.getChildByName("game").getChildByName("bgListJP").getChildByName("nameGame");
            const gameID = group.rooms[0]?.gameID;

            switch (gameID) {
                case 213:
                    gameNameLabel.string = App.instance.getTextLang("tx_than_tai");
                    gameNameNode.on(Node.EventType.TOUCH_END, () => {
                        // Global.LobbyController.actGoToCaiShen();
                        this.dismiss();
                    });
                    break;
                case 201:
                    gameNameLabel.string = App.instance.getTextLang("tx_vuong_quoc");
                    gameNameNode.on(Node.EventType.TOUCH_END, () => {
                        // Global.LobbyController.actGotoKingdom();
                        this.dismiss();
                    });
                    break;
                case 207:
                    gameNameLabel.string = App.instance.getTextLang("tx_thuy_cung");
                    break;
                case 203:
                    gameNameLabel.string = App.instance.getTextLang("tx_sam_truyen");
                    break;
                case 215:
                    gameNameLabel.string = App.instance.getTextLang("tx_vu_truong");
                    break;
                case 205:
                    gameNameLabel.string = App.instance.getTextLang("tx_gai_nhay");
                    gameNameNode.on(Node.EventType.TOUCH_END, () => {
                        // Global.LobbyController.actGoToDancing();
                        this.dismiss();
                    });
                    break;
                case 211:
                    gameNameLabel.string = App.instance.getTextLang("tx_rung_vang");
                    gameNameNode.on(Node.EventType.TOUCH_END, () => {
                        // Global.LobbyController.actGotoForest();
                        this.dismiss();
                    });
                    break;
                case 107:
                    gameNameLabel.string = App.instance.getTextLang("txt_resurrection");
                    logItem.getChildByName("game").getChildByName("icon").active = false;
                    gameNameNode.on(Node.EventType.TOUCH_END, () => {
                        // Global.LobbyController.actGamePhucSinh();
                        this.dismiss();
                    });
                    break;
                case 101:
                    gameNameLabel.string = App.instance.getTextLang("txt_mini_poker");
                    logItem.getChildByName("game").getChildByName("icon").active = false;
                    gameNameNode.on(Node.EventType.TOUCH_END, () => {
                        // Global.LobbyController.actGameMiniPoker();
                        this.dismiss();
                    });
                    break;
            }

            group.rooms.forEach(room => {
                const roomNode = logItem.getChildByName("game").getChildByName(`room${room.roomID}`);
                if (roomNode) {
                    roomNode.getChildByName("fun").getComponent(Label).string = room.jackpotFund.toLocaleString("vi-VN");
                }
            });

            const avatarGame = logItem.getChildByName("game").getChildByName("bgListJP").getChildByName("avaGame");
            const sprite = avatarGame.getComponent(Sprite);
            const spriteFrame = logoMap[gameID];
            if (sprite && spriteFrame) sprite.spriteFrame = spriteFrame;
        });
    }

    groupJackpotData(data: JackpotItem[]): GroupedJackpot[] {
        const grouped: { [key: string]: GroupedJackpot } = {};
        data.forEach(item => {
            if (!grouped[item.gameName]) {
                grouped[item.gameName] = { gameName: item.gameName, rooms: [] };
            }
            grouped[item.gameName].rooms.push(item);
        });
        return Object.values(grouped);
    }

    // formatDate(dateStr: string): string {
    //     return new Date(dateStr).toLocaleString();
    // }
}
