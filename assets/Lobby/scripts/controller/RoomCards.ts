// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import { _decorator, Component, EditBox, instantiate, Label, log, RichText,Node, UIOpacity } from "cc";
import BuyChipPoker from "../common/BuyChipPoker";
import Configs from "../common/Config";
import CasinoPopupRank from "../common/casino/Casino.PopupRank";
import ChatHubSignalRClient from "../common/networks/ChatHubSignalRClient";
import App from "../common/App";
import CardGameSignalRClient from "../common/networks/CardGameSignalRClient";



const { ccclass, property } = _decorator;

@ccclass
export default class RoomCards extends Component {
  static instance: RoomCards = null;

  @property(Node)
  tabNode: Node;

  @property([Node])
  tabContents: Node[] = [];
  gameID = -1;
  CurrencyID = 1;

  @property(BuyChipPoker)
  buyChip: BuyChipPoker;

  chatChannel = "";
  @property(Node)
  contentMsg: Node = null;
  @property(Node)
  itemMsg: Node = null;
  @property(EditBox)
  editBoxChat: EditBox = null;
  @property(Label)
  lblNamegame: Label = null;
  dictName = {};

  protected onLoad(): void {
    this.dictName[Configs.GameId88.TLMN] = "na3";
    this.dictName[Configs.GameId88.TLMNSolo] = "na5";
    this.dictName[Configs.GameId88.SamLoc] = "na4";
    this.dictName[Configs.GameId88.SamLocSolo] = "na6";
    this.dictName[Configs.GameId88.BaCay] = "na1";
    this.dictName[Configs.GameId88.Poker] = "na2";
    this.dictName[Configs.GameId88.MauBinh] = "na7";
    this.dictName[Configs.GameId88.Catte] = "na8";
  }

  setDataRoom(gameID) {
    this.gameID = gameID;
    this.lblNamegame.string = "";
    if (this.dictName[gameID]) {
      this.lblNamegame.string = Configs.GameId88.getGameName(gameID).toUpperCase();
    }
    this.node.getComponent(CasinoPopupRank).gameId = gameID;
    this.chatChannel = Configs.GameId.getChatChannelName(gameID);
    this.contentMsg.removeAllChildren();
    ChatHubSignalRClient.getInstance().registerChat(this.chatChannel, (_response) => { });
    ChatHubSignalRClient.getInstance().receiveChat((response) => {
      for (var i = 0; i < response.length; i++) {
        const data = response[i];
        if (data.i !== this.chatChannel) {
          return;
        }

        this.editBoxChat.string = "";
        var item = instantiate(this.itemMsg);
        if (data.v >= 6) {
          item.getComponent(RichText).string = `<color=#ffffff>${data.c}</color>`;
        } else if (`${data.a}:${data.p}` === `${Configs.Login.AccountID}:${Configs.Login.PortalID}`) {
          item.getComponent(RichText).string = `<color=#fff600>${data.n}: </c><color=#ffffff>${data.c}</color>`;
        } else {
          item.getComponent(RichText).string = `<color=#3c91e6>${data.n}: </c><color=#ffffff>${data.c}</color>`;
        }
        this.contentMsg.addChild(item);
      }
    });

    for (let index = 0; index < 12; index++) {
      var dataVip = Configs.App.G88_CONFIG["ListRoomVip"][this.gameID];
      var dataNonal = Configs.App.G88_CONFIG["ListRoomNormal"][this.gameID];

      if (dataVip[index].IsLock) {

        this.tabContents[0].children[index].getComponent(UIOpacity).opacity = 130;
      }
      else {
        this.tabContents[0].children[index].on("click", () => {
          log(dataVip[index].Value);
          this.actJoinRoom(dataVip[index].Value);
        });
      }

      if (dataNonal[index].IsLock) {
        this.tabContents[1].children[index].getComponent(UIOpacity).opacity = 130;
      } else {
        this.tabContents[1].children[index].on("click", () => {
          log(dataNonal[index].Value);
          this.actJoinRoom(dataNonal[index].Value);
        });
      }

    }


  }

  sendMessage() {
    var content = this.editBoxChat.string;
    if (content == "") {
      return;
    }

    ChatHubSignalRClient.getInstance().sendChat(this.chatChannel, content, (_response) => {
      this.editBoxChat.string = "";
    });
  }

  start() {
    RoomCards.instance = this;
    CardGameSignalRClient.getInstance().receiveArray('joinGame', (data1: any, data2: any) => {
      this.onHandleJoin(data1, data2);
    });
    this.initTab();
    if (this.gameID === Configs.GameId88.Catte) {
      CardGameSignalRClient.getInstance().send('EnterGame', [Configs.Login.CurrencyID], (data) => {
        if (data && data.r) {
          log(data.r);
          App.instance.showLoading(false);
        }
      });
    } else {
      CardGameSignalRClient.getInstance().send('EnterLobby', [Configs.Login.CurrencyID], (data) => {
        log(data);
        App.instance.showLoading(false);
      });
    }


  }

  actBack() {
    if (this.gameID === Configs.GameId88.Catte) {
      CardGameSignalRClient.getInstance().send('ExitGame', [], (data) => {
        if (data && data.r) {
          log(data.r);
        }
        CardGameSignalRClient.getInstance().close();
      });
    } else {
      CardGameSignalRClient.getInstance().send('ExitLobby', [], (data) => {
        log(data);

        CardGameSignalRClient.getInstance().close();
      });
    }
    this.node.destroy();
  }

  onHandleJoin(data, extra) {
    App.instance.DataPass = [data, extra];
    switch (data.GameId) {
      case Configs.GameId88.TLMN:
      case Configs.GameId88.TLMNSolo:
        App.instance.openPrefabGame("TienLen", "TienLen", (bundle, prefab) => {
          App.instance.showLoading(false);
          App.instance.addGameToNode(prefab);

        });
        break;
      case Configs.GameId88.MauBinh:
        App.instance.openPrefabGame("MauBinh", "MauBinh", (bundle, prefab) => {
          App.instance.showLoading(false);
          App.instance.addGameToNode(prefab);

        });
        // App.instance.openGame("MauBinh", "MauBinh");
        break;
      case Configs.GameId88.Poker:
        App.instance.openPrefabGame("Poker", "Poker", (bundle, prefab) => {
          App.instance.showLoading(false);
          App.instance.addGameToNode(prefab);

        });
        //  App.instance.openGame("Poker", "Poker");
        break;
      case Configs.GameId88.SamLoc:
      case Configs.GameId88.SamLocSolo:
        App.instance.openPrefabGame("Sam", "Sam", (bundle, prefab) => {
          App.instance.showLoading(false);
          App.instance.addGameToNode(prefab);

        });
        // App.instance.openGame("Sam", "Sam");
        break;
      case Configs.GameId88.BaCay:
        App.instance.openPrefabGame("ba_cay", "ba_cay", (bundle, prefab) => {
          App.instance.showLoading(false);
          App.instance.addGameToNode(prefab);

        });
        // App.instance.openGame("ba_cay", "ba_cay");
        break;
      case Configs.GameId88.Catte:
        App.instance.openPrefabGame("Catte", "Catte", (bundle, prefab) => {
          App.instance.showLoading(false);
          App.instance.addGameToNode(prefab);

        });
        // App.instance.openGame("ba_cay", "ba_cay");
        break;
      default:
        break;
    }

  }




  initTab() {
    for (let index = 0; index < this.tabNode.children.length; index++) {

      this.tabNode.children[index].on("click", () => {
        this.onClickTab(index);
      });
    }
    this.onClickTab(0);
  }

  onClickTab(index1) {

    for (let index = 0; index < this.tabNode.children.length; index++) {

      this.tabNode.children[index].children[1].active = index1 == index ? true : false;
      this.tabContents[index].active = index1 == index ? true : false;

    }
    this.CurrencyID = 1 - index1;

    log("EnterLobby" + this.CurrencyID);



  }

  actJoinRoom(value) {
    if (this.gameID == -1) {
      return;
    }
    //preload game trước xử lí lỗi vào nhưng ko nhận data vi đang tải

    App.instance.DataPass = [];
    if (this.gameID == Configs.GameId88.Poker) {
      this.buyChip.showWithData(value, this.CurrencyID, true);
    } else {
      App.instance.showLoading(true);
      log("PlayNow" + this.CurrencyID);

      let cb = (data) => {
        App.instance.showLoading(false);
        log(data);
        if (data == -1) {
          App.instance.ShowAlertDialog(App.instance.getTextLang("st12"));
        }
      }

      if (this.gameID === Configs.GameId88.Catte) {
        CardGameSignalRClient.getInstance().send('EnterRoom', [value, this.CurrencyID], (data) => {
          if (data && data.r) {
            cb(data);
            this.onHandleJoin(data.r, null);
          }
        });
      } else {
        CardGameSignalRClient.getInstance().send('PlayNow', [value, this.CurrencyID], (data) => {
          cb(data);
        });
      }
    }


  }
  //   itemNode.children[6].on("click", () => {
  //                         this.onClickDetail(results[index]);
  //                                         });


  protected onDestroy(): void {
    CardGameSignalRClient.getInstance().dontReceive();
    ChatHubSignalRClient.getInstance().dontReceive();
  }

  // update (dt) {}
}
