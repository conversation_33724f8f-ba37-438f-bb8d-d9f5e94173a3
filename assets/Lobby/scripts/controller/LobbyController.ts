import {
    _decorator,
    Component,
    instantiate,
    log,
    Node,
    view,
    ResolutionPolicy,
    sys,
    UIOpacity,
    game,
    Label,
    AudioSource,
    System,
    Tween,
    tween,
    v3,
    easing,
    Animation,
    random,
    Toggle,
    Prefab,
    assetManager,
    ImageAsset,
    SpriteFrame,
    Texture2D,
    Sprite,
    Widget,
    Color,
    Vec3,
    <PERSON><PERSON><PERSON>,
    director
} from "cc";

import BundleControl from "../../../Loading/scripts/BundleControl";
import App from "../common/App";
import Http from "../common/Http";
import { Utils } from "db://assets/Lobby/scripts/common/Utils";
import { SPUtils } from "db://assets/Lobby/scripts/common/SPUtils";
import Configs from "db://assets/Lobby/scripts/common/Config";
import Config from "db://assets/Lobby/scripts/common/Config";
import { BroadcastReceiver } from "db://assets/Lobby/scripts/common/BroadcastListener";
import eventBus from "db://assets/Lobby/scripts/common/EventBus";
import { Global } from "db://assets/Lobby/scripts/common/Global";
import { PopupProfile } from "db://assets/Lobby/scripts/controller/lobby/PopupProfile";
import { PopupRegister } from "db://assets/Lobby/scripts/controller/lobby/PopupRegister";
import { PopupUpdateNickname } from "db://assets/Lobby/scripts/controller/lobby/PopupUpdateNickname";
import LobbyLoginByOTP from "db://assets/Lobby/scripts/controller/lobby/Lobby.LoginByOTP";
import PopupDownload from "db://assets/Lobby/scripts/controller/lobby/PopupDownload";
import { PopupSupport } from "db://assets/Lobby/scripts/controller/lobby/PopupSupport";
import PopupEvent from "db://assets/Lobby/scripts/controller/lobby/PopupEvent";
import PopupGiftCode from "db://assets/Lobby/scripts/controller/lobby/PopupGiftCode";
import PopupForgetPassword from "db://assets/Lobby/scripts/controller/lobby/Lobby.PopupForgetPassword";
import PopupSecurity from "./lobby/security/Lobby.PopupSecurity";
import { TweenUtils } from "../common/TweenUtils";
import { PopupTournament } from "db://assets/Lobby/scripts/controller/lobby/PopupTournament";
import PopupEventX2 from "db://assets/Lobby/scripts/controller/lobby/PopupEventX2";
import CardGameSignalRClient from "../common/networks/CardGameSignalRClient";

declare global {
    interface Window {
        forge: any;
        photon: any;
        md5: any;
        signalR: any;
    }
}

const { ccclass, property } = _decorator;

@ccclass("LobbyController")
export class LobbyController extends Component {
    @property(Node)
    nodeWheel: Node = null;
    @property(Node)
    nodeRoom: Node = null;
    @property(Label)
    lblNickname: Label = null;
    @property(Sprite)
    sprAvatar: Sprite = null;
    @property(Label)
    lblGoldBalance: Label = null;
    @property(Label)
    lblCoinBalance: Label = null;

    @property({ type: [Node] })
    hoverCategory: Node[] = [];
    @property(EditBox)
    userName: EditBox = null;
    @property(EditBox)
    password: EditBox = null;
    @property(Node)
    panelLoggedOut: Node = null;
    @property(Node)
    panelLoggedIn: Node = null;
    @property(Node)
    panelMenu: Node = null;

    @property(Node)
    jackpotx6: Node = null;
    @property(Node)
    jackpotX6List: Node = null;
    @property(Node)
    jackpotX6Content: Node = null;

    @property(SpriteFrame)
    logo_fortune: SpriteFrame | null = null;
    @property(SpriteFrame)
    logo_kingdom: SpriteFrame | null = null;
    @property(SpriteFrame)
    logo_ocean: SpriteFrame | null = null;
    @property(SpriteFrame)
    logo_oracle: SpriteFrame | null = null;
    @property(SpriteFrame)
    logo_dancingNight: SpriteFrame | null = null;
    @property(SpriteFrame)
    logo_dancing: SpriteFrame | null = null;
    @property(SpriteFrame)
    logo_forest: SpriteFrame | null = null;

    @property(SpriteFrame)
    icon_x2: SpriteFrame | null = null;
    @property(SpriteFrame)
    icon_x3: SpriteFrame | null = null;
    @property(SpriteFrame)
    icon_x4: SpriteFrame | null = null;
    @property(SpriteFrame)
    icon_x5: SpriteFrame | null = null;
    @property(SpriteFrame)
    icon_x6: SpriteFrame | null = null;

    @property(Node)
    containerLoginBox: Node = null;
    @property(Node)
    bgLoginBox: Node = null;
    @property(Node)
    LoginBox: Node = null;
    @property(EditBox)
    userNameInBox: EditBox = null;
    @property(EditBox)
    passwordInBox: EditBox = null;

    private pingpongID: number = null;
    private isVisibleJackPotX6: boolean = false;


    protected onLoad(): void {
        this.scheduleOnce(()=>{
            console.log(Configs.App.DOMAIN_CONFIG);
        },10)
        Global.LobbyController = this;

        this.fixMultiScreen();
        this.fetchAndDecryptMessage();

        this.hoverCategory.forEach((node) => {
            node.on(Node.EventType.MOUSE_ENTER, () => this.onHoverEnter(node), this);
            node.on(Node.EventType.MOUSE_LEAVE, () => this.onHoverLeave(node), this);
        });
    }

    protected start(): void {
        BroadcastReceiver.register(
            BroadcastReceiver.USER_UPDATE_COIN,
            () => {
                Http.get(Configs.App.DOMAIN_CONFIG['GetAllBalance'], {}, (status, json) => {
                    if (status === 200) {
                        Configs.Login.GoldBalance = json['d'][0]['goldBalance'];
                        Configs.Login.CoinBalance = json['d'][0]['coinBalance'];

                        this.lblCoinBalance.string = Utils.formatNumber(Configs.Login.CoinBalance);
                        this.lblGoldBalance.string = Utils.formatNumber(Configs.Login.GoldBalance);
                    }
                });
            },
            this
        );

        BroadcastReceiver.register(
            BroadcastReceiver.USER_LOGGED_IN,
            () => {
                this.lblNickname.string = Configs.Login.Nickname;
                this.sprAvatar.spriteFrame = App.instance.getAvatarSpriteFrame(
                    Configs.Login.Avatar
                );

                this.panelLoggedOut.active = false;
                this.panelLoggedIn.active = true;

                this.lblCoinBalance.string = Utils.formatNumber(Configs.Login.CoinBalance);
                this.lblGoldBalance.string = Utils.formatNumber(Configs.Login.GoldBalance);

                Configs.Login.IsLogin = true;
                eventBus.emit('LOGIN_SUCCESS');
                this.LoginBox.active = false;


                // App.instance.openGame(Configs.InGameIds.TaiXiuMini);
            },
            this
        );

        BroadcastReceiver.register(
            BroadcastReceiver.USER_INFO_REFRESH,
            (data: any) => {
                if (data) {
                    Config.Login.set(data);
                } else {
                    Http.get(Configs.App.DOMAIN_CONFIG['GetAccountInfoUrl'], {}, (status, res) => {
                        if (status === 200) {
                            Config.Login.set(res.d);
                            this.sprAvatar.spriteFrame = App.instance.getAvatarSpriteFrame(
                                Configs.Login.Avatar
                            );
                            this.lblCoinBalance.string = Utils.formatNumber(Configs.Login.CoinBalance);
                            this.lblGoldBalance.string = Utils.formatNumber(Configs.Login.GoldBalance);
                        }
                    });
                }
            },
            this
        );

        BroadcastReceiver.register(
            BroadcastReceiver.USER_LOGOUT,
            (_data: any) => {
                if (this.pingpongID) {
                    clearInterval(this.pingpongID);
                }

                // SignalRClient.closeAll();
                if (director.getScene().name !== "Lobby") {
                    // App.instance.loadScene("Lobby");
                } else {
                    this.panelLoggedOut.active = true;
                    this.panelLoggedIn.active = false;
                    this.userName.string = "";
                    this.password.string = "";
                    this.userNameInBox.string = "";
                    this.passwordInBox.string = "";
                }
                SPUtils.setUserName("");
                SPUtils.setUserPass("");
                eventBus.emit('LOGOUT');
                Configs.Login.clear();
                // App.instance.buttonMiniGame?.actHidden();
                // App.instance.bigGameNode?.removeAllChildren();
                // App.instance.miniGame?.removeAllChildren();
                // this.nodeRoom?.removeAllChildren();
            },
            this
        );
    }
    actGoToTLMN() {
        if (!Configs.Login.IsLogin) {
            App.instance.alertDialog.showMsg(
                App.instance.getTextLang("txt_need_login")
            );
            return;
        }
        // App.instance.showLoading(true);
        App.instance.openPrefabGame("TienLen", "TienLen", (bundle, prefab) => {
            // App.instance.showLoading(true);
            CardGameSignalRClient.getInstance().connectHub("tlmn", (success) => {
                // App.instance.showLoading(false);
                if (success) {
                    this.nodeRoom.removeAllChildren();
                    let cb = (prefab) => {
                        let popupDaily = instantiate(prefab)
                            .getComponent("RoomCards");
                        this.nodeRoom.addChild(popupDaily.node);
                        popupDaily.setDataRoom(Configs.GameId88.TLMN);

                    };
                    BundleControl.loadPrefabPopup("prefabs/RoomCards", cb);
                } else {
                    App.instance.showErrLoading(App.instance.getTextLang("me11"));
                }

            });
        });


    }
    actVQMM() {
        Http.get(Configs.App.DOMAIN_CONFIG["VQMM_GetTurnUrl"], { type: 0 }, (status, res) => {
            try {

                if (status === 200 && res.c == 0) {
                    if (+res?.d > 0) {
                        this.showWheelOfFortune();
                    }
                }

            } catch (error) {

            }
        });
        this.actHiddenMiniGameTipZo();
    }
    actHiddenMiniGameTipZo() {

    }
    showVipWheel() {

        let cb = (prefab) => {
            let popupDaily = instantiate(prefab)
                .getComponent("PopupVipWheel");
            this.nodeWheel.addChild(popupDaily.node);

        };
        BundleControl.loadPrefabPopup("prefabs/PopupVipWheel", cb);
    }
    showWheelOfFortune() {

        let cb = (prefab: any) => {
            let popupSpinWheel = instantiate(prefab).getComponent("PopupSpinWheel");
            App.instance.node.addChild(popupSpinWheel.node);
            popupSpinWheel.show();
        };
        BundleControl.loadPrefabPopup("prefabs/PopupSpinWheel", cb);

    }

    fixMultiScreen() {
        if (
            view.getViewportRect().width / view.getViewportRect().height >
            1920 / 1080
        ) {
            view.setDesignResolutionSize(1920, 1080, ResolutionPolicy.FIXED_HEIGHT);
        } else {
            view.setDesignResolutionSize(1920, 1080, ResolutionPolicy.FIXED_WIDTH);
        }
    }

    fetchAndDecryptMessage() {
        try {
            const key = Utils.isBrowser() ? Config.App.G88_KEY_DECRYPT_CONFIG : Config.App.G88_KEY_DECRYPT_CONFIG_APP;
            const nonSecretPayloadLength = 0;

            Http.get(Config.App.G88_DOMAIN_GET_CONFIG, {}, (status, res) => {
                if (status != 200) {
                    this.fetchAndDecryptMessage();

                    return;
                }

                const encryptedMessage = res.replace(/[^A-Za-z0-9+/=]/g, '');
                const decodedKey = window.forge.util.decode64(key);
                const cipherText = window.forge.util.decode64(encryptedMessage);
                let offset = 0;
                offset += nonSecretPayloadLength;
                const nonce = cipherText.slice(offset, offset + 16);
                offset += 16;
                const authTag = cipherText.slice(cipherText.length - 16);
                const encryptedMessagePart = cipherText.slice(offset, cipherText.length - 16);
                const decipher = window.forge.cipher.createDecipher('AES-GCM', decodedKey);
                decipher.start({
                    iv: nonce,
                    tagLength: 128,
                    tag: window.forge.util.createBuffer(authTag)
                });
                decipher.update(window.forge.util.createBuffer(encryptedMessagePart));
                const success = decipher.finish();
                if (!success) {
                    throw new Error('Decryption failed');
                }

                const data = JSON.parse(decipher.output.toString());
                log(data)
                Configs.App.DOMAIN_CONFIG = data.DomainConfig;
                Configs.App.G88_CONFIG = data;
                Config.App.GAME_AVAILABLE_IDS = data.GamesAvaiable;

                SPUtils.setRSAPublicKey(data.RsaPublicKey);
                Utils.setStorageValue('client-token', data.Token);

                if (SPUtils.getUserName() != "" && SPUtils.getUserPass() != "") {
                    this.actLogin(SPUtils.getUserName(), SPUtils.getUserPass());
                }
            }, false);

        } catch (error) {
            if (error instanceof Error) {
                log(`Error: ${error}`);
            } else {
                log('An unknown error occurred');
            }
        }
    }

    public actLogin(username = null, password = null): void {
        if (Configs.App.DOMAIN_CONFIG.length === 0) return;

        if (username == null || password == null) {
            if (this.LoginBox.active) {
                username = this.userNameInBox.string.trim();
                password = this.passwordInBox.string.trim();
            } else {
                username = this.userName.string.trim();
                password = this.password.string.trim();
            }
        }

        if (username.length == 0) {
            App.instance.alertDialog.showMsg(
                App.instance.getTextLang("txt_login_username_not_blank")
            );
            return;
        }

        if (password.length == 0) {
            App.instance.alertDialog.showMsg(
                App.instance.getTextLang("txt_login_password_not_blank")
            );
            return;
        }

        const data = {
            Username: username,
            Password: password,
            Timestamp: Utils.getTicks(),
            Md5Password: window.md5(password)
        }

        const payload = {
            "Input": Utils.encryptWithRSA(JSON.stringify(data)),
        };

        App.instance.showLoading(true);
        Http.post(Configs.App.DOMAIN_CONFIG['LoginUrl'], payload, (_status, res) => {
            if (this.pingpongID) {
                clearInterval(this.pingpongID);
            }
            this.pingpongID = setInterval(() => {
                this.pingpong();
            }, 60000);

            App.instance.showLoading(false);
            switch (parseInt(res["c"])) {
                case 0:
                    Configs.Login.IsLogin = true;
                    SPUtils.setUserName(username);
                    SPUtils.setUserPass(password);

                    Configs.Login.AccessToken = res["m"];
                    Configs.Login.SessionKey = res["d"]["sessionToken"];

                    BroadcastReceiver.send(BroadcastReceiver.USER_INFO_REFRESH, res['d']);
                    BroadcastReceiver.send(BroadcastReceiver.USER_LOGGED_IN);
                    break;
                case 2:
                    Configs.Login.Username = res.p[0];
                    BundleControl.loadPrefabPopup("prefabs/PopupLoginByOTP", (prefab: any) => {
                        let popup = instantiate(prefab).getComponent(LobbyLoginByOTP);
                        App.instance.popupNode.addChild(popup.node);
                        popup.show();
                    });
                    break;
                case 3:
                    Configs.Login.Username = res.p[0];
                    BundleControl.loadPrefabPopup("prefabs/PopupUpdateNickName", (prefab: any) => {
                        let popup = instantiate(prefab).getComponent(PopupUpdateNickname);
                        App.instance.popupNode.addChild(popup.node);
                        popup.show();
                    });
                    break;
                default:
                    break;
            }
        });
    }

    public actRegister(): void {
        if (Configs.App.DOMAIN_CONFIG.length === 0) return;

        BundleControl.loadPrefabPopup("prefabs/PopupRegister", (prefab: any) => {
            let popup = instantiate(prefab).getComponent(PopupRegister);
            App.instance.popupNode.addChild(popup.node);
            popup.show();
        });
    }

    public actLostPassword(): void {
        if (Configs.App.DOMAIN_CONFIG.length === 0) return;

        BundleControl.loadPrefabPopup("prefabs/PopupForgetPassword", (prefab: any) => {
            let popup = instantiate(prefab).getComponent(PopupForgetPassword);
            App.instance.popupNode.addChild(popup.node);
            popup.show();
        });
    }

    public actLogout(): void {
        App.instance.confirmDialog.showMsg(
            App.instance.getTextLang("txt_ask_logout"),
            (isConfirm) => {
                if (isConfirm) {
                    BroadcastReceiver.send(BroadcastReceiver.USER_LOGOUT);
                }
            }
        );
    }

    private pingpong() {
        const accessToken = Configs.Login.AccessToken;
        if (accessToken == "") {
            return;
        }

        Http.post(Configs.App.DOMAIN_CONFIG['CheckAuthenUrl'], {}, (_status, res) => {
            switch (res['c']) {
                case 0:
                    if (res['m'] != null) {
                        Configs.Login.AccessToken = res['m'];
                    }
                    break;
                case 2:
                    Configs.Login.Username = res.p[0];
                    BundleControl.loadPrefabPopup("prefabs/PopupLoginByOTP", (prefab: any) => {
                        let popup = instantiate(prefab).getComponent(LobbyLoginByOTP);
                        App.instance.popupNode.addChild(popup.node);
                        popup.show();
                    });
                    break;
                case 3:
                    Configs.Login.Username = res.p[0];
                    BundleControl.loadPrefabPopup("prefabs/PopupUpdateNickName", (prefab: any) => {
                        let popup = instantiate(prefab).getComponent(PopupUpdateNickname);
                        App.instance.popupNode.addChild(popup.node);
                        popup.show();
                    });
                    break;
                default:
                    BroadcastReceiver.send(BroadcastReceiver.USER_LOGOUT);
            }
        })
    }

    onHoverEnter(node: Node) {
        tween(node)
            .to(0.1, { scale: new Vec3(1.1, 1.1, 1.1) })
            .start();
    }

    onHoverLeave(node: Node) {
        tween(node)
            .to(0.1, { scale: new Vec3(1, 1, 1) })
            .start();
    }

    toggleMenu() {
        this.panelMenu.active = !this.panelMenu.active;
    }

    openPopupProfile(even: Event, customEventData: string) {
        if (!Configs.Login.IsLogin) {
            this.actShowLoginForm();
            return;
        }
        const index = parseInt(customEventData);
        BundleControl.loadPrefabPopup("prefabs/PopupProfile", (prefab: any) => {
            let popup = instantiate(prefab).getComponent(PopupProfile);
            App.instance.popupNode.addChild(popup.node);
            popup.show();
            popup.setTabDefault(index);
        });
    }

    actDownloadGameTipZo() {
        if (!Configs.Login.IsLogin) {
            this.actShowLoginForm();
            return;
        }
        BundleControl.loadPrefabPopup("prefabs/PopupDownload", (prefab: any) => {
            let popup = instantiate(prefab).getComponent(PopupDownload);
            if (!popup) {
                log("PopupDownload component not found on prefab.");
                return;
            }
            App.instance.popupNode.addChild(popup.node);
            popup.show();
        });
    }

    actSPGameTipZo() {
        if (!Configs.Login.IsLogin) {
            this.actShowLoginForm();
            return;
        }
        BundleControl.loadPrefabPopup("prefabs/PopupSupport", (prefab: any) => {
            let popup = instantiate(prefab).getComponent(PopupSupport);
            if (!popup) {
                log("PopupSupport component not found on prefab.");
                return;
            }
            App.instance.popupNode.addChild(popup.node);
            popup.show();
        });
    }

    actMissonGameTipZo(even, data) {
        if (!Configs.Login.IsLogin) {
            this.actShowLoginForm();
            return;
        }
        BundleControl.loadPrefabPopup("prefabs/PopupEvent", (prefab: any) => {
            let popup = instantiate(prefab).getComponent(PopupEvent);
            if (!popup) {
                log("PopupSupport component not found on prefab.");
                return;
            }
            App.instance.popupNode.addChild(popup.node);
            popup.show();
        });
    }

    actOpenGifCode(even, data) {
        if (!Configs.Login.IsLogin) {
            this.actShowLoginForm();
            return;
        }
        BundleControl.loadPrefabPopup("prefabs/PopupGiftCode", (prefab: any) => {
            let popup = instantiate(prefab).getComponent(PopupGiftCode);
            if (!popup) {
                log("PopupSupport component not found on prefab.");
                return;
            }
            App.instance.popupNode.addChild(popup.node);
            popup.show();
        });
    }

    actTournament(even, data) {
        if (!Configs.Login.IsLogin) {
            this.actShowLoginForm();
            return;
        }
        BundleControl.loadPrefabPopup("prefabs/PopupTournament", (prefab: any) => {
            let popup = instantiate(prefab).getComponent(PopupTournament);
            if (!popup) {
                log("PopupSupport component not found on prefab.");
                return;
            }
            App.instance.popupNode.addChild(popup.node);
            popup.show();
        });
    }

    actEventX2(even: Event, customEventData: string) {
        if (!Configs.Login.IsLogin) {
            this.actShowLoginForm();
            return;
        }
        const index = parseInt(customEventData);
        BundleControl.loadPrefabPopup("prefabs/PopupX2", (prefab: any) => {
            let popup = instantiate(prefab).getComponent(PopupEventX2);
            if (!popup) {
                log("PopupSupport component not found on prefab.");
                return;
            }
            App.instance.popupNode.addChild(popup.node);
            popup.show();
            popup.showTab(index);
        });
    }


    actToggleTargetX6() {
        this.isVisibleJackPotX6 = !this.isVisibleJackPotX6;
        this.jackpotx6.active = this.isVisibleJackPotX6;

        const logoMap = {
            213: this.logo_fortune,
            201: this.logo_kingdom,
            207: this.logo_ocean,
            203: this.logo_oracle,
            215: this.logo_dancingNight,
            205: this.logo_dancing,
            211: this.logo_forest,
        };

        if (this.isVisibleJackPotX6) {
            this.jackpotX6List.removeAllChildren();
            Http.get(Configs.App.DOMAIN_CONFIG['GetListJackpot'], { CurrencyID: Configs.Login.CurrencyID }, (status, res) => {
                if (status == 200) {
                    res.d.forEach((item, index) => {
                        if (item.multiplier > 0) {
                            let itemJackpot = instantiate(this.jackpotX6Content);
                            let nameLabel = itemJackpot.getChildByName("center").getChildByName("header").getComponent(Label);
                            switch (item.gameID) {
                                case 213:
                                    nameLabel.string = App.instance.getTextLang("tx_than_tai");
                                    break;
                                case 201:
                                    nameLabel.string = App.instance.getTextLang("tx_vuong_quoc");
                                    break;
                                case 207:
                                    nameLabel.string = App.instance.getTextLang("tx_thuy_cung");
                                    break;
                                case 203:
                                    nameLabel.string = App.instance.getTextLang("tx_sam_truyen");
                                    break;
                                case 215:
                                    nameLabel.string = App.instance.getTextLang("tx_vu_truong");
                                    break;
                                case 205:
                                    nameLabel.string = App.instance.getTextLang("tx_gai_nhay");
                                    break;
                                case 211:
                                    nameLabel.string = App.instance.getTextLang("tx_rung_vang");
                                    break;
                            }
                            itemJackpot.getChildByName("center").getChildByName("room").getChildByName("value").getComponent(Label).string = item.roomID == 1 ? "100" : (item.roomID == 2 ? "1.000" : "10.000");
                            itemJackpot.getChildByName("center").getChildByName("content").getChildByName("X").getComponent(Label).string = "X" + item.multiplier;
                            itemJackpot.getChildByName("center").getChildByName("content").getChildByName("nextJackpot").getComponent(Label).string = item.nextJackpot;
                            const logoNode = itemJackpot.getChildByName("logo");
                            const iconNode = itemJackpot.getChildByName("icon");
                            const spriteIcon = iconNode.getComponent(Sprite);

                            switch (item.multiplier) {
                                case 2:
                                    spriteIcon.spriteFrame = this.icon_x2;
                                    break;
                                case 3:
                                    spriteIcon.spriteFrame = this.icon_x3;
                                    break;
                                case 4:
                                    spriteIcon.spriteFrame = this.icon_x4;
                                    break;
                                case 5:
                                    spriteIcon.spriteFrame = this.icon_x5;
                                    break;
                                case 6:
                                    spriteIcon.spriteFrame = this.icon_x6;
                                    break;
                            }


                            const sprite = logoNode.getComponent(Sprite);
                            if (sprite && logoMap[item.gameID]) {
                                sprite.spriteFrame = logoMap[item.gameID];
                            }

                            itemJackpot.on(Node.EventType.TOUCH_END, (event: any) => {
                                // event.stopPropagation();
                                if (!Configs.Login.IsLogin) {
                                    this.actShowLoginForm();
                                    return;
                                }
                            });

                            this.jackpotX6List.addChild(itemJackpot);
                        }
                    });
                }
            });
        }
    }

    actTeleTipZo() {
        sys.openURL("https://t.me/TIPZO_CSKH_BOT");
    }

    actFbTipZo() {
        sys.openURL("http://tiny.cc/ple7001");
    }

    actSecurity() {
        if (!Configs.Login.IsLogin) {
            // this.actShowLoginForm();
            return;
        }
        BundleControl.loadPrefabPopup("prefabs/PopupSecurity", (prefab: any) => {
            let popup = instantiate(prefab).getComponent(PopupSecurity);
            if (!popup) {
                log("PopupSecurity component not found on prefab.");
                return;
            }
            App.instance.popupNode.addChild(popup.node);
            popup.show();
        });
    }

    actCheckLogined() {
        if (!Configs.Login.IsLogin) {
            this.LoginBox.active = true;
            return;
        }
    }

    actShowLoginForm() {
        this.LoginBox.active = true;

        let editsOutside = this.node.parent.getComponentsInChildren(EditBox);
        for (let edit of editsOutside) {
            edit.tabIndex = -1;
        }

        let editsInside = this.containerLoginBox.getComponentsInChildren(EditBox);
        for (let i = 0; i < editsInside.length; i++) {
            editsInside[i].tabIndex = i + 1; // Đảm bảo thứ tự focus đúng
        }

        if (editsInside.length > 0) {
            editsInside[0].focus();
        }
    }


    actCloseLoginForm() {
        if (!this.bgLoginBox || !this.containerLoginBox) return;

        const uiOpacityLoginBox = this.bgLoginBox.getComponent(UIOpacity);
        tween(uiOpacityLoginBox)
            .to(0.2, { opacity: 0 }) // fadeOut trong 0.2s
            .start();

        // Reset giá trị ban đầu
        this.containerLoginBox.getComponent(UIOpacity)!.opacity = 255;
        this.containerLoginBox.setScale(1, 1, 1); // scale = 1

        const uiOpacity = this.containerLoginBox.getComponent(UIOpacity);
        if (uiOpacity) {
            tween(uiOpacity)
                .to(0.3, { opacity: 150 }, { easing: easing.backIn })
                .start();
        }

        tween(this.containerLoginBox)
            .to(0.3, { scale: new Vec3(0.8, 0.8, 1) }, { easing: easing.backIn })
            .call(() => {
                this._onDismissed();
            })
            .start();
    }

    actGameTaiXiuMD5Live() {
        App.instance.openGame(Configs.InGameIds.TaiXiuMD5Live);
    }

    actGameTaiXiuLive() {
        App.instance.openGame(Configs.InGameIds.TaiXiuLive);
    }

    _onDismissed() {
        if (!this.containerLoginBox || !this.LoginBox || !this.node.parent) return;

        const edits = this.containerLoginBox.getComponentsInChildren(EditBox);
        for (let edit of edits) {
            edit.tabIndex = -1;
        }

        this.bgLoginBox.getComponent(UIOpacity)!.opacity = 210;
        this.containerLoginBox.getComponent(UIOpacity)!.opacity = 255;
        this.containerLoginBox.setScale(1, 1, 1);

        const editsOutside = this.node.parent.getComponentsInChildren(EditBox);
        for (let i = 0; i < editsOutside.length; i++) {
            editsOutside[i].tabIndex = i + 1;
        }

        if (this.userNameInBox) this.userNameInBox.string = '';
        if (this.passwordInBox) this.passwordInBox.string = '';

        this.LoginBox.active = false;
    }
}
