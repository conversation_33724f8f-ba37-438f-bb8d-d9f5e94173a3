// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import { _decorator, Component, Sprite, UITransform, v3, Vec2, Vec3 ,Node, Tween, tween } from "cc";
import CartList from "./CardList";

const {ccclass, property} = _decorator;

@ccclass
export default class Card extends Component {

    canTouch:boolean = true;
    rank:number;
    suit:any;
    cardIndex:number=0;
    idCard:number=0;
    isSelect:boolean = false;
    isMoved:boolean =false;
    origin:Vec3 ;
    isTouched:boolean = false;
    cardDistance:number = 0;

    preTouchPoint:Vec2;

    @property(Sprite)
    sprite:Sprite;

    cardList:CartList;

    

    setCardWithId (cardId) {
        this.rank =  cardId.rank;
       
        this.suit =  cardId.suit;
       console.log(this.rank.toString()+this.suit.toString());
    }


    getIDCardSend(){
        
       
        return  this.idCard;
       
    }

    
    start() {
        var thiz = this;
        this.node.getComponent(UITransform).width;
        this.node.on(
            Node.EventType.TOUCH_START,
            (event) => {
                
               if(thiz.canTouch && !thiz.isTouched){
                thiz.preTouchPoint = event.touch.getLocation();
                thiz.isTouched = true;
                thiz.isMoved = false;
                // var p = this.node.getComponent(UITransform).convertToNodeSpaceAR(event.touch);
                // console.log("Node.EventType.TOUCH_START"+ p);
               }
               
            },
            this
          );
        this.node.on(
            Node.EventType.TOUCH_MOVE,
            (event) => {
                // console.log("Node.EventType.TOUCH_MOVE"+thiz.cardIndex);
                if(!thiz.canTouch) return;
                var p =  event.touch.getLocation();
                var izz = false;
                if (!thiz.isMoved) {
                 
                    if (Vec2.distance(thiz.preTouchPoint,p) < 5.0) {
                        return;
                    }
                    else {
                         thiz.node.setSiblingIndex(200);
                         izz = true;
                        thiz.isMoved = true;
                    }
                }

        
                var x = (p.x - thiz.preTouchPoint.x)/thiz.node.parent.getScale().x;
                var y = (p.y - thiz.preTouchPoint.y)/thiz.node.parent.getScale().x;
                thiz.node.setPosition(v3( thiz.node.position.x+ x,thiz.node.position.y+ y,0));
                thiz.preTouchPoint = p;
               
        
                var dx =thiz.node.position.x - thiz.origin.x;
                if (Math.abs(dx) > thiz.cardDistance) {
                    if (dx > 0) {
                        thiz.cardList.swapCardRight(thiz.cardIndex);
                    }
                    else {
                        thiz.cardList.swapCardLeft(thiz.cardIndex);
                    }
                }
            },
            this
          );
      
          this.node.on(
            Node.EventType.TOUCH_END,
            (event) => {
            
                thiz.onTouchEnd(event);
               
            },
            this
          );
          this.node.on(
            Node.EventType.TOUCH_CANCEL,
            (event) => {
            
                thiz.onTouchEnd(event);
            },
            this
          );
    }

    onTouchEnd(event){
        
        var thiz = this;
        if(!thiz.canTouch) return;
        this.isTouched = false;

        if(thiz.isMoved){
            thiz.moveToOriginPosition();
            // thiz.node.setSiblingIndex(thiz.cardIndex);
           thiz.cardList.reOrderSibingIndex();
          
          
            
        }else{
            thiz.isSelect = !thiz.isSelect;
            thiz.setSelected(thiz.isSelect,false);
            thiz.cardList.onSelected(this,thiz.isSelect );
        }
        // var zz = "";
        // thiz.cardList.cardList.forEach(element => {
        //     zz = zz+ element.cardIndex.toString()+ ",";
        // });
        // console.log(zz);
    }

    moveToOriginPosition () {
        if (!this.isTouched) {
            Tween.stopAllByTarget(this.node);
            var thiz = this;
            thiz.node.setSiblingIndex(thiz.cardIndex+100);
           
            tween(thiz.node). to(0.2, { position: thiz.origin }).call(()=>{
                thiz.node.setSiblingIndex(thiz.cardIndex);
            }).start();

           
            // var beforeMove = new CallFunc(function () {
            //     thiz.getParent().reorderChild(thiz, thiz.cardIndex + 100);
            // });
            // var afterMove = new CallFunc(function () {
            //     thiz.getParent().reorderChild(thiz, thiz.cardIndex);
            // });
            // var move = new MoveTo(0.2, p(this.origin.x, this.origin.y));
            // this.runAction(new Sequence(beforeMove, move, afterMove));
          
            this.isSelect = false;
        }
    }
    

    setSelected(selected, force) {
        if (force) {
            Tween.stopAllByTarget(this.node);
             this.node.setPosition(this.origin);
        }

        this.isSelect = selected;
        if (selected) {
            this.node.setPosition(v3( this.origin.x,this.origin.y+50));
        }
        else {
            this.node.setPosition(this.origin);
        }

    }
    
    getFrameSprite(nameCard){

    }

   updateFrameCard(){

 
      this.sprite.spriteFrame =  this.cardList.cardFrames.children[this.idCard].getComponent(Sprite).spriteFrame;
   }

    initCard(card,idCard,_cardList,_origin,canTouch,inew){
         this.setCardWithId(card);
         this.idCard = idCard;
        this.cardIndex = inew;
        
        this.cardList = _cardList;
        this.origin = _origin;
        this.canTouch = canTouch;
    }

    update(deltaTime: number) {
        
    }

    // update (dt) {}
}
