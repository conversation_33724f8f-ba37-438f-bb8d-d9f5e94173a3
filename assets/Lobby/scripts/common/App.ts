import {
    _decorator,
    Component,
    Node,
    Size,
    Label,
    SpriteFrame,
    instantiate,
    easing,
    Tween,
    tween,
    Prefab,
} from "cc";
import AlertDialog from "db://assets/Lobby/scripts/common/AlertDialog";
import { LanguageManager } from "db://assets/Lobby/scripts/common/language/Language.LanguageManager";
import ConfirmDialog from "db://assets/Lobby/scripts/common/ConfirmDialog";
import Config from "db://assets/Lobby/scripts/common/Config";
import MiniGameTX1SignalRClient from "db://assets/Lobby/scripts/common/networks/MiniGameTX1SignalRClient";
import MiniGameTX2SignalRClient from "db://assets/Lobby/scripts/common/networks/MiniGameTX2SignalRClient";
import MiniGameTX3SignalRClient from "db://assets/Lobby/scripts/common/networks/MiniGameTX3SignalRClient";
import MiniGameSignalRClient from "db://assets/Lobby/scripts/common/networks/MiniGameSignalRClient";
import BundleControl from "db://assets/Loading/scripts/BundleControl";
import { MiniGame } from "db://assets/Lobby/scripts/common/MiniGame";
import AudioManager from "./AudioManager";
import { BroadcastReceiver } from "./BroadcastListener";
import MiniGameTXMD5SignalRClient from "db://assets/Lobby/scripts/common/networks/MiniGameTXMD5SignalRClient";
import KingdomSignalRClient from "./networks/KingdomSignalRClient";
import OceanSignalRClient from "./networks/OceanSignalRClient";
import DQSignalRClient from "./networks/DQSignalRClient";
import ForestSignalRClient from "./networks/ForestSignalRClient";

const { ccclass, property } = _decorator;

@ccclass("App")
export default class App extends Component {
    gotoGameFromVqmn(gameID: number) {
        throw new Error("Method not implemented.");
    }

    @property(Node)
    public canvas: Node | null = null;
    static instance: App = null;
    inactivityTimer = 0;
    DataPass = [];

    @property
    designResolution: Size = new Size(1920, 1080);
    @property([SpriteFrame])
    sprFrameAvatars: Array<SpriteFrame> = new Array<SpriteFrame>();
    @property(AlertDialog)
    alertDialog: AlertDialog = null;
    @property(ConfirmDialog)
    confirmDialog: ConfirmDialog = null;
    @property(Node)
    loadingNode: Node = null;
    @property(Label)
    loadingLabel: Label = null;
    @property(Node)
    bigGameNode: Node = null;
    @property(Node)
    miniGameNode: Node = null;
    @property(Node)
    popupNode: Node = null;
    @property(Node)
    tipzoJackpotEventX2X6Node: Node = null;
    @property(Node)
    tipzoMiniLiveNode: Node = null;
    @property(Node)
    alertToast: Node = null;
    @property([Node])
    allNodeGame: Node[] = [];

    public isShowNotifyJackpot = true;
    private timeOutLoading: any = null;
    private taiXiuJackpotMiniGame: MiniGame = null;
    private taiXiuMD5MiniGame: MiniGame = null;
    private gameNodeMap: Map<number, Node[]> = new Map();

    protected onLoad() {
        if (App.instance != null) {
            this.node.destroy();
            return;
        }
        App.instance = this;

        const gameIds = [
            Config.GameAvailableIds.SamLocSolo,
            Config.GameAvailableIds.BaCay,
            Config.GameAvailableIds.Catte,
            Config.GameAvailableIds.TLMN,
            Config.GameAvailableIds.MauBinh,
            Config.GameAvailableIds.Poker,
            Config.GameAvailableIds.TLMNSolo,
            Config.GameAvailableIds.SamLoc,
            Config.GameAvailableIds.Sedie,
            Config.GameAvailableIds.NewSicbo,
            Config.GameAvailableIds.NewRoulette,
            Config.GameAvailableIds.DragonTiger,
            Config.GameAvailableIds.Baccarat,
            Config.GameAvailableIds.Blackjack,
            Config.GameAvailableIds.Kingdom,
            Config.GameAvailableIds.Olympia,
            Config.GameAvailableIds.Ocean,
            Config.GameAvailableIds.Forest,
            Config.GameAvailableIds.GodOfFortune,
            Config.GameAvailableIds.Dancing,
            Config.GameAvailableIds.Disco,
            Config.GameAvailableIds.SoDo,
            Config.GameAvailableIds.SpaceWar,
            Config.GameAvailableIds.Sortie,
            Config.GameAvailableIds.Shark,
            Config.GameAvailableIds.TieuLongNgu,
            Config.GameAvailableIds.BanCa,
            Config.GameAvailableIds.PokerTournament,
            Config.GameAvailableIds.ABC, //chua ro game nay
            Config.GameAvailableIds.TournamentGoOn,
            Config.GameAvailableIds.TournamentOTT,
            Config.GameAvailableIds.MultiLuckyDiceLive,
            Config.GameAvailableIds.LuckyDiceMd5Live,
            Config.GameAvailableIds.SedieLive,
            Config.GameAvailableIds.MegaMillions,
            Config.GameAvailableIds.PowerBall,
            Config.GameAvailableIds.Keno,
            Config.GameAvailableIds.OTT,//xoso
            Config.GameAvailableIds.FantasySport,
            Config.GameAvailableIds.SportVirtual,
            Config.GameAvailableIds.VQMM, //MiniGame
            Config.GameAvailableIds.MiniPoker,
            Config.GameAvailableIds.TaiXiu,
            Config.GameAvailableIds.LuckyDiceMd5,
            Config.GameAvailableIds.HiLo,
            Config.GameAvailableIds.BauCua,
            Config.GameAvailableIds.PhucSinh,
            Config.GameAvailableIds.LuckyWild,
            Config.GameAvailableIds.OTT,
        ];


        this.allNodeGame.forEach((node, index) => {
            const gameId = gameIds[index];
            if (gameId != null) {
                if (!this.gameNodeMap.has(gameId)) {
                    this.gameNodeMap.set(gameId, []);
                }
                this.gameNodeMap.get(gameId).push(node);
                (node as any).gameId = gameId;
            }
        });


        this.setActiveGameById(Config.GameAvailableIds.ABC, false);

    }

    gotoLobby() {
        AudioManager.getInstance().turnOnMusic();
        this.bigGameNode.removeAllChildren();
        this.alertDialog.dismiss();
        this.alertToast.active = false;
        BroadcastReceiver.send(BroadcastReceiver.USER_UPDATE_COIN);
        Tween.stopAllByTarget(this.alertToast);
    }

    getAvatarSpriteFrame(avatar: string): SpriteFrame {
        let avatarInt = parseInt(avatar);

        if (
            isNaN(avatarInt) ||
            avatarInt < 0 ||
            avatarInt >= this.sprFrameAvatars.length
        ) {
            return this.sprFrameAvatars[0];
        }

        return this.sprFrameAvatars[avatarInt];
    }


    public getTextLang(key: string) {
        return LanguageManager.instance.getString(key);
    }

    showLoading(isShow: boolean, timeOut: number = 15) {
        if (!this.loadingNode || !this.loadingLabel) return;
        const lastChild = this.node.children[this.node.children.length - 1];
        this.loadingNode.setSiblingIndex(lastChild?.getSiblingIndex() + 1 || 0);

        this.loadingLabel.string = App.instance.getTextLang("IS_LOADING");

        if (this.timeOutLoading != null) {
            clearTimeout(this.timeOutLoading);
        }

        if (isShow) {
            if (timeOut > 0) {
                this.timeOutLoading = setTimeout(() => {
                    this.showLoading(false);
                }, timeOut * 1000);
            }
            this.loadingNode.active = true;
        } else {
            this.loadingNode.active = false;
        }
    }

    public ShowAlertDialog(mess: string) {
        this.alertDialog.showMsg(mess);
    }

    showErrLoading(msg?: string) {
        this.showLoading(true, 5);
        this.loadingLabel.string = msg ? msg : "Mất kết nối, đang thử lại...";
    }

    openGame(gameId: number) {
        switch (gameId) {
            case Config.InGameIds.TaiXiuMini:
                this.actGameTaiXiuMiniGame();
                break;
            case Config.InGameIds.TaiXiuLive:
                this.actGameTaiXiuLive();
                break;
            case Config.InGameIds.TaiXiuMD5:
                this.actGameTaiXiuMD5MiniGame();
                break;
            case Config.InGameIds.TaiXiuMD5Live:
                this.actGameTaiXiuMD5Live();
                break;
            case Config.InGameIds.XoSo:
                this.actXoSoMiniGame();
                break;
            case Config.InGameIds.Kingdom:
                this.actGameVuongQuoc();
                break;
            case Config.InGameIds.ThuyCung:
                this.actGameThuyCung();
                break;
            case Config.InGameIds.RungVang:
                this.actGameRungVang();
                break;
            case Config.InGameIds.GaiNhay:
                this.actGameGaiNhay();
                break;
            case Config.InGameIds.ThanTai:
                this.actGameThanTai();
                break;
            case Config.InGameIds.VuTruong:
                this.actGameVuTruong();
                break;
            default:
                break;
        }
    }


    openPrefabGame(bundleName, sceneName, callback) {
        BundleControl.loadPrefabGame(bundleName, sceneName,
            (finish, total) => {
                this.showLoading(true);
            },
            (prefab, bundle) => {
                this.showLoading(false);
                bundle.loadDir("res/prefabs", Prefab, (finish, total) => { },
                    (err, arrPrefab) => {
                        callback(bundle, prefab);
                    }
                );
            }
        );
    }

    addGameToNode(game) {
        let node = instantiate(game);
        this.bigGameNode.addChild(node);
    }

    actGameTaiXiuMiniGame() {
        const showError = () => {
            App.instance.showErrLoading(App.instance.getTextLang("me11"));
        };

        const connectHub = (
            client: { connectHub: (cb: (success: boolean) => void) => void }
        ): Promise<void> => {
            return new Promise((resolve, reject) => {
                client.connectHub((success: boolean) => {
                    App.instance.showLoading(false);
                    success ? resolve() : reject();
                });
            });
        };

        connectHub(MiniGameTX1SignalRClient.getInstance())
            .then(() => connectHub(MiniGameTX2SignalRClient.getInstance()))
            .then(() => connectHub(MiniGameTX3SignalRClient.getInstance()))
            .then(() => connectHub(MiniGameSignalRClient.getInstance()))
            .then(() => {
                if (this.taiXiuJackpotMiniGame == null) {
                    BundleControl.loadPrefabGame("TaiXiuDouble", "TaiXiuDouble", (finish, total) => {
                        // @TODO with global loading
                        this.showLoading(true, 0);
                    },
                        (prefab) => {
                            this.showLoading(false);
                            if (this.taiXiuJackpotMiniGame == null) {
                                let node = instantiate(prefab);
                                node.parent = this.miniGameNode;
                                node.active = false;

                                this.taiXiuJackpotMiniGame = node.getComponent(MiniGame);
                            }
                            this.taiXiuJackpotMiniGame.show();
                        }
                    );
                } else {
                    this.taiXiuJackpotMiniGame.show();
                }
            })
            .catch(showError);
    }

    actGameTaiXiuMD5MiniGame() {
        const showError = () => {
            App.instance.showErrLoading(App.instance.getTextLang("me11"));
        };

        const connectHub = (
            client: { connectHub: (cb: (success: boolean) => void) => void }
        ): Promise<void> => {
            return new Promise((resolve, reject) => {
                client.connectHub((success: boolean) => {
                    App.instance.showLoading(false);
                    success ? resolve() : reject();
                });
            });
        };

        connectHub(MiniGameTXMD5SignalRClient.getInstance())
            .then(() => {
                if (this.taiXiuMD5MiniGame == null) {
                    BundleControl.loadPrefabGame("TaiXiuMD5", "TaiXiuMD5", (finish, total) => {
                        // @TODO with global loading
                        this.showLoading(true, 0);
                    },
                        (prefab) => {
                            this.showLoading(false);
                            if (this.taiXiuMD5MiniGame == null) {
                                let node = instantiate(prefab);
                                node.parent = this.miniGameNode;
                                node.active = false;

                                this.taiXiuMD5MiniGame = node.getComponent(MiniGame);
                            }
                            this.taiXiuMD5MiniGame.show();
                        }
                    );
                } else {
                    this.taiXiuMD5MiniGame.show();
                }
            })
            .catch(showError);
    }

    actGameTaiXiuLive() {
        const showError = () => {
            App.instance.showErrLoading(App.instance.getTextLang("me11"));
        };

        const connectHub = (
            client: { connectHub: (cb: (success: boolean) => void) => void }
        ): Promise<void> => {
            return new Promise((resolve, reject) => {
                client.connectHub((success: boolean) => {
                    App.instance.showLoading(false);
                    success ? resolve() : reject();
                });
            });
        };

        connectHub(MiniGameTX1SignalRClient.getInstance())
            .then(() => connectHub(MiniGameTX2SignalRClient.getInstance()))
            .then(() => connectHub(MiniGameTX3SignalRClient.getInstance()))
            .then(() => connectHub(MiniGameSignalRClient.getInstance()))
            .then(() => {
                BundleControl.loadPrefabGame("TaiXiuLive", "TaiXiuLive", (finish, total) => {
                    // @TODO with global loading
                    this.showLoading(true, 0);
                },
                    (prefab) => {
                        this.showLoading(false);
                        this.bigGameNode.removeAllChildren();
                        let node = instantiate(prefab);
                        node.active = true;
                        node.removeComponent("TaiXiuLiveMD5.Controller");
                        this.bigGameNode.addChild(node);
                    }
                );
            })
            .catch(showError);
    }

    actGameTaiXiuMD5Live() {
        const showError = () => {
            App.instance.showErrLoading(App.instance.getTextLang("me11"));
        };

        const connectHub = (
            client: { connectHub: (cb: (success: boolean) => void) => void }
        ): Promise<void> => {
            return new Promise((resolve, reject) => {
                client.connectHub((success: boolean) => {
                    App.instance.showLoading(false);
                    success ? resolve() : reject();
                });
            });
        };

        connectHub(MiniGameTXMD5SignalRClient.getInstance())
            .then(() => {
                BundleControl.loadPrefabGame("TaiXiuLive", "TaiXiuLive", (finish, total) => {
                    // @TODO with global loading
                    this.showLoading(true, 0);
                },
                    (prefab) => {
                        this.showLoading(false);
                        this.bigGameNode.removeAllChildren();
                        let node = instantiate(prefab);
                        node.active = true;
                        node.removeComponent("TaiXiuLiveJP.Controller");
                        this.bigGameNode.addChild(node);
                    }
                );
            })
            .catch(showError);
    }

    actXoSoMiniGame() {
        BundleControl.loadPrefabPopup("prefabs/PopupXoSo", (prefab: any) => {
            let popup = instantiate(prefab).getComponent("PopupMiniGameXoSo");
            App.instance.popupNode.addChild(popup.node);
            popup.show();
        });
    }

    actGameVuongQuoc() {
        const showError = () => {
            App.instance.showErrLoading(App.instance.getTextLang("me11"));
        };

        const connectHub = (
            client: { connectHub: (cb: (success: boolean) => void) => void }
        ): Promise<void> => {
            return new Promise((resolve, reject) => {
                client.connectHub((success: boolean) => {
                    App.instance.showLoading(false);
                    success ? resolve() : reject();
                });
            });
        };
        connectHub(KingdomSignalRClient.getInstance())
            .then(() => {
                BundleControl.loadPrefabGame("Kingdom", "Kingdom", (finish, total) => {
                    // @TODO with global loading
                    this.showLoading(true, 0);
                },
                    (prefab) => {
                        this.showLoading(false);
                        this.bigGameNode.removeAllChildren();
                        let node = instantiate(prefab);
                        node.active = true;
                        this.bigGameNode.addChild(node);
                    }
                );
            })
            .catch(showError);
    }

    actGameThuyCung() {
        const showError = () => {
            App.instance.showErrLoading(App.instance.getTextLang("me11"));
        };

        const connectHub = (
            client: { connectHub: (cb: (success: boolean) => void) => void }
        ): Promise<void> => {
            return new Promise((resolve, reject) => {
                client.connectHub((success: boolean) => {
                    App.instance.showLoading(false);
                    success ? resolve() : reject();
                });
            });
        };
        connectHub(OceanSignalRClient.getInstance())
            .then(() => {
                BundleControl.loadPrefabGame("Ocean", "Ocean", (finish, total) => {
                    // @TODO with global loading
                    this.showLoading(true, 0);
                },
                    (prefab) => {
                        this.showLoading(false);
                        this.bigGameNode.removeAllChildren();
                        let node = instantiate(prefab);
                        node.active = true;
                        this.bigGameNode.addChild(node);
                    }
                );
            })
            .catch(showError);
    }

    actGameGaiNhay() {
        const showError = () => {
            App.instance.showErrLoading(App.instance.getTextLang("me11"));
        };

        const connectHub = (
            client: { connectHub: (cb: (success: boolean) => void) => void }
        ): Promise<void> => {
            return new Promise((resolve, reject) => {
                client.connectHub((success: boolean) => {
                    App.instance.showLoading(false);
                    success ? resolve() : reject();
                });
            });
        };

        connectHub(DQSignalRClient.getInstance())
            .then(() => {
                BundleControl.loadPrefabGame("DancingQueen", "DancingQueen", (finish, total) => {
                    // @TODO with global loading
                    this.showLoading(true, 0);
                },
                    (prefab) => {
                        this.showLoading(false);
                        this.bigGameNode.removeAllChildren();
                        let node = instantiate(prefab);
                        node.active = true;
                        this.bigGameNode.addChild(node);
                    }
                );
            })
            .catch(showError);
    }

    actGameThanTai() {
        // TODO
    }

    actGameVuTruong() {
        // TODO
    }

    actGameRungVang() {
        const showError = () => {
            App.instance.showErrLoading(App.instance.getTextLang("me11"));
        };

        const connectHub = (
            client: { connectHub: (cb: (success: boolean) => void) => void }
        ): Promise<void> => {
            return new Promise((resolve, reject) => {
                client.connectHub((success: boolean) => {
                    App.instance.showLoading(false);
                    success ? resolve() : reject();
                });
            });
        };

        connectHub(ForestSignalRClient.getInstance())
            .then(() => {
                BundleControl.loadPrefabGame("Forest", "Forest", (finish, total) => {
                    // @TODO with global loading
                    this.showLoading(true, 0);
                },
                    (prefab) => {
                        this.showLoading(false);
                        this.bigGameNode.removeAllChildren();
                        let node = instantiate(prefab);
                        node.active = true;
                        this.bigGameNode.addChild(node);
                    }
                );
            })
            .catch(showError);

    }


    public showToast(msg: string) {
        this.alertToast.active = true;
        this.alertToast.getComponent(Label).string = msg;

        Tween.stopAllByTarget(this.alertToast);
        tween(this.alertToast)
            .set({ position: this.alertToast.position.set(this.alertToast.position.x, 0, 0) })
            .to(2.0, { position: this.alertToast.position.set(this.alertToast.position.x, 100, 0) }, { easing: easing.sineOut })
            .call(() => {
                this.alertToast.active = false;
            })
            .start();
    }

    public setActiveGameById(gameId: number, active: boolean) {
        const nodes = this.gameNodeMap.get(gameId);
        if (nodes) {
            nodes.forEach(node => node.active = active);
        }
    }

}
