import { _decorator, assetManager, Component, Sprite, SpriteFrame, Texture2D } from "cc";


    const {ccclass, property} = _decorator;

@ccclass
export default class AutoPlayAnimation extends Component {
    private sprite: Sprite = null;
    private spriteFrames: SpriteFrame[] = [];
    private index: number = 0;
    private frameRate: number = 0.1; // 100ms mỗi frame
    private isPlaying: boolean = false;

    onLoad() {
        this.sprite = this.getComponent(Sprite);
        this.loadAnimationFrames();
    }

    private loadAnimationFrames() {
        const bundle = assetManager.getBundle("TienLen");
        if (!bundle) {
            console.error("Không tìm thấy bundle 'TienLen'");
            return;
        }

        bundle.loadDir("res/win", Texture2D, (err, textures) => {
            if (err) {
                console.error("Lỗi khi tải ảnh:", err);
                return;
            }

            // Chuyển texture thành SpriteFrame
            this.spriteFrames = textures.map((texture: Texture2D) => {
                      const spriteFrame = new SpriteFrame();
                        spriteFrame.texture = texture;
                        return spriteFrame;

            });

            // Bắt đầu animation nếu node đang active
            if (this.node.active && this.spriteFrames.length > 0) {
                this.startAnimation();
            }
        });
    }

    private startAnimation() {
        if (this.isPlaying || this.spriteFrames.length === 0) return;

        this.isPlaying = true;
        this.index = 0; // Reset animation về frame đầu tiên
        this.schedule(this.updateAnimation, this.frameRate);
    }

    private stopAnimation() {
        this.isPlaying = false;
        this.unschedule(this.updateAnimation);
    }

    private updateAnimation() {
        if (!this.node.active) return; // Dừng cập nhật nếu node bị ẩn

        this.sprite.spriteFrame = this.spriteFrames[this.index];
        this.index = (this.index + 1) % this.spriteFrames.length;
    }

    onEnable() {
        if (this.spriteFrames.length > 0) {
            this.startAnimation(); // Khi node được kích hoạt, restart animation từ đầu
        }
    }

    onDisable() {
        this.stopAnimation(); // Khi node bị tắt, dừng animation
    }
}
