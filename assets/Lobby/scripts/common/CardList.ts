// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import { _decorator, Component, v3, Vec3,Node, SpriteFrame ,Enum, Vec2, UITransform, game, Game, Tween, tween, color, Sprite, log, instantiate, size } from "cc";
import Card from "./MCard";
import { TLMNUtility } from "./TLMNUtility";

const {ccclass, property} = _decorator;

// const AnchorType = Enum({
//     LEFT: 0,
//     CENTER: 1,
//     RIGTH: 2
// });
enum AnchorType {
    LEFT = 0,
    CENTER= 1,
    RIGTH = 2
}



enum DirectionType {
    HORIZONTAL,
    VERTICAL
}



enum PlayviewType {
    ME,
    OTHER
}

@ccclass
export default class CartList extends Component {

    deckPoint: Vec3 = v3(0,0,0);
    cardList: Card[] = [];
    @property
    canTouch: boolean = true;

    @property (Node )
    cardClone: Node = null;
    cardFrameBack:SpriteFrame ;

    width: number;
    height: number;

    @property (Node )
    cardFrames: Node = null;
   
    @property({type: Enum(AnchorType) })
    anchorType: AnchorType = AnchorType.CENTER;
    @property({type:Enum(DirectionType)})
    direction: DirectionType = DirectionType.HORIZONTAL;
    @property({type:Enum(PlayviewType)})
    playeviewType: PlayviewType = PlayviewType.OTHER;

    init(pointDec,cardCl ,framCardBack,cardFrame){

        this.cardFrameBack = framCardBack;
        this.cardClone = cardCl;
        let worldPos = pointDec.getComponent(UITransform).convertToWorldSpaceAR(Vec2.ZERO);
        this.deckPoint = this.node.getComponent(UITransform).convertToNodeSpaceAR(worldPos);
        this.cardFrames = cardFrame;
    }
   
    isbackground = false;
    start() {
       // this.linkCard = App.instance.linkCard;
        this.width = this.node.getComponent(UITransform).width;
        this.height = this.node.getComponent(UITransform).height;

        game.on(Game.EVENT_HIDE, this.onGamePause, this);

        game.on(Game.EVENT_SHOW, this.onGameResume, this);
    }

    onGamePause() {
        this.isbackground = true;
        console.log("Game is paused, moving to background");
        // Xử lý khi game bị đưa xuống nền
    }
    
    onGameResume() {
        this.isbackground = false;
        console.log("Game is resumed, coming back from background");
        // Xử lý khi game quay lại từ nền
    }

    update(deltaTime: number) {

    }

    removeAll() {
        this.cardList = [];
        this.node.removeAllChildren();
    }

    // addCard(cardNode: any) {
    //     // if (!this.cardSize) {
    //     //     this.cardSize = size(card.touchRect.width, card.touchRect.height);
    //     //     if (this.cardSize.height > this.getContentSize().height) {
    //     //         var ratio = this.getContentSize().height / this.cardSize.height;
    //     //         this.cardSize.width *= ratio;
    //     //         this.cardSize.height *= ratio;
    //     //     }
    //     // }
    //     // if (card.touchRect.height > this.cardSize.height) {
    //     //     card.setScale(this.cardSize.height / card.touchRect.height);
    //     // }
    //     // card.cardIndex = this.cardList.length;
    //     //card.origin = p(0, 0);
    //      card.canTouch = false;
    //     cardNode.parent =  this.node;
    //    var card = cardNode.getComponent(Card);
    //    card.initCard("",null,this);
    //     this.cardList.push(card);
    // }
    onSelected (card, isSelected) {

    }

    removeCard (cards:any) {
        var arrCard = [];
        for (var i = 0; i < cards.length; i++) {
            var rank = cards[i].rank;
            var suit = cards[i].suit;
            for (var j = 0; j < this.cardList.length; j++) {
                var card = this.cardList[j];
                if (card.rank == rank && card.suit == suit) {
                    // var p = this.node.getComponent(UITransform).convertToWorldSpaceAR(card.node.getPosition());
                    // card.setPosition(p);
                    card.canTouch = false;
                    // card.node.retain();
                    // card.node.removeFromParent(true);
                    arrCard.push(card);
                    this.cardList.splice(j, 1);
                    break;
                }
            }
        }
        return arrCard;
    }

    removeCardWithID (ids:any) {
        var arrCard = [];
        for (var i = 0; i < ids.length; i++) {
           
            for (var j = 0; j < this.cardList.length; j++) {
                var card = this.cardList[j];
                if (card.idCard == ids[i]) {
                    // var p = this.node.getComponent(UITransform).convertToWorldSpaceAR(card.node.getPosition());
                    // card.setPosition(p);
                    card.canTouch = false;
                    // card.node.retain();
                    // card.node.removeFromParent(true);
                    arrCard.push(card);
                    this.cardList.splice(j, 1);
                }
            }
        }
        this.reOrder();
        return arrCard;
    }

    getCardSelected(){

        var cardSelected = [];
        for (var i = 0; i < this.cardList.length; i++) {
            if (this.cardList[i].isSelect) {
                cardSelected.push(this.cardList[i]);
            }
        }
        return cardSelected;
        
    }
    getCardsByArray(ids){
        var cardSelected = [];
        for (var j = 0; j < ids.length;j++) {
            for (var i = 0; i < this.cardList.length; i++) {
                if (this.cardList[i].idCard== ids[j]) {
                    cardSelected.push(this.cardList[i]);
                   break;
                }
            }
        }
        return cardSelected;
        
    }

    swapCardLeft(index) {

        if (index > 0) {
            var cardMove = this.cardList[index];
            var cardSwap = this.cardList[index - 1];
            this.swapCard(cardMove, cardSwap);
        }
    }
    swapCardRight (index) {
        if (index < this.cardList.length - 1) {
            var cardMove = this.cardList[index];
            var cardSwap = this.cardList[index + 1];
            this.swapCard(cardMove, cardSwap);
        }
    }
    swapCard(card1, card2) {
        var _origin = card1.origin;
        var _cardIndex = card1.cardIndex;

        card1.origin = card2.origin;
        card1.cardIndex = card2.cardIndex;

        card2.origin = _origin;
        card2.cardIndex = _cardIndex;
        card2.moveToOriginPosition();
        card1.moveToOriginPosition();
        

        this.cardList[card1.cardIndex] = card1;
        this.cardList[card2.cardIndex] = card2;
    }

    reOrder(){
        if(this.cardList.length<1){
            return;
        }
        var tranfromCard = this.cardList[0].node;
        var cardWith = this.height * tranfromCard.getComponent(UITransform).width /tranfromCard.getComponent(UITransform).height;
        var distanceX = (this.node.getComponent(UITransform).width - cardWith ) / (this.cardList.length-1);
        if (distanceX > cardWith) {
            distanceX = cardWith;
        }
        distanceX = 2/3*cardWith;
      for (let index = 0; index < this.cardList.length; index++) {
        const element = this.cardList[index];
        // element.cardDistance = distanceX;
        
      

        Tween.stopAllByTarget(element.node);
  
       var posOrigin = new Vec3(index * distanceX + cardWith / 2,  this.height / 2, 0);
      
        element.cardIndex = index;
        element.origin = posOrigin;
       tween(element.node). to(0.2, { position: element.origin }).call(()=>{
        element.node.getComponent(Card).isSelect = false;
        element.node.setSiblingIndex(element.cardIndex);
        }).start();
       
      }
        
    }

    reOrderSibingIndex(){
       
      for (let index = 0; index < this.cardList.length; index++) {
        const element = this.cardList[index];
        element.node.setSiblingIndex(element.cardIndex);

      }
        
    }

    onFinishDealCard(){

    }
    deactiveCard(){
        this.cardList.forEach(element => {
        //  log("mo card");
          element.node.getComponent(Sprite).color = color(127, 127, 127,255);
        });
      }
    onStartDealOneCard(){
        
    }
    dealCards(cards: any,  animation) {
        log("dealCards");
        var thiz = this;
        var cardClone =  this.cardClone;
        var tranfromCard = cardClone;
        var cardWith = this.height * tranfromCard.getComponent(UITransform).width /tranfromCard.getComponent(UITransform).height;
        
        this.removeAll();
        var distanceX = (this.node.getComponent(UITransform).width - cardWith ) / (cards.length-1);
        if (distanceX > cardWith) {
            distanceX = cardWith;
        }
        distanceX = 2/3*cardWith;
        var y = thiz.height / 2;

        for (var i = 0; i < cards.length; i++) {

            (()=>{
                var inew  = i;
                var cardNode = instantiate(cardClone);
                var card = cardNode.getComponent(Card);
                thiz.cardList.push(card);
                cardNode.parent =  thiz.node;
                cardNode.setPosition(thiz.deckPoint);
                card.cardDistance = distanceX;
                cardNode.getComponent(UITransform).setContentSize(size(cardWith,thiz.height));
                var posOrigin = new Vec3(inew * distanceX + cardWith / 2, y, 0);
                card.initCard(thiz.getCardWithId(cards[inew]),cards[inew],thiz,posOrigin,animation?false:this.canTouch,inew);

                if (animation && !this.isbackground) {
                    
                     card.sprite.spriteFrame = thiz.cardFrameBack;
    
                    card.node.setScale(v3(-1, 1, 1));
                    let t1 = tween(card.node).delay(0.02*inew).call(() => {
                        card.node.active = true;
                        thiz.onStartDealOneCard();
                    });
                    let t2 = tween(card.node)
                        .to(0.25, { position: posOrigin });
                    let t3 = tween(card.node).to(0.1, {
                        // Bind position
                         scale: new Vec3(0, card.node.scale.y, card.node.scale.z),
                    }
                    ).call(() => {
                        setTimeout(() => {
                            card.updateFrameCard();
                        }, 0); 
                    });
    
                    let t4 = tween(card.node).delay(0.1).to(0.1, {
                        // Bind position
                       scale: new Vec3(1, card.node.scale.y, card.node.scale.z),
                    }
                    ).call(()=>{
                        card.canTouch = thiz.canTouch;
                        if(inew == cards.length-1){
                            thiz.onFinishDealCard();
                        }
                    });
    
                    tween(card.node).sequence(t1, t2, t3, t4).start();
    
    
                }
                else {
                    
                    card.node.active = true;
                    card.updateFrameCard();
                    card.canTouch = thiz.canTouch;
                    card.node.setPosition(posOrigin);
                    log("finihs delcar " + inew + "=---" + (cards.length-1));
                    if(inew == cards.length-1){
                        log("finihs delcar");
                        thiz.onFinishDealCard();
                    }
                }
            })();
           


        }
    }
    tlmnUtil:TLMNUtility = new TLMNUtility();
    isTypeSortRank:boolean = true;
    reArrangeCards (sortFunc:any) {
        if(this.isTypeSortRank){
            if (!sortFunc) {
                this.cardList.sort(function (a, b) {
                    return a.rank - b.rank;
                });
            } else {
                this.cardList.sort(sortFunc);
            }
            this.reOrder();
        }else{

            var cardNew = [];
            this.cardList.forEach(element => {
                cardNew.push({rank:element.rank,suit:element.suit})
            });
            var cardOrder = this.tlmnUtil.orderCard(cardNew);


            // this.cardList = this.tlmnUtil.orderCard(this.cardList);

            this.cardList.sort(function(a, b){  
                return cardOrder.findIndex(i=>(a.rank == i.rank && a.suit == i.suit)) - cardOrder.findIndex(i=>(b.rank == i.rank && b.suit == i.suit));
              });
            // if (!sortFunc) {
            //     this.cardList.sort(function (a, b) {
            //         return a.rank - b.rank;
            //     });
            // } else {
            //     this.cardList.sort(sortFunc);
            // }
            this.reOrder();
        }
        this.isTypeSortRank = !this.isTypeSortRank;


    }
   arrCardOld = [8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,26,27,24,25,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,0,1,2,3,4,5,6,7];
    getCardWithId (cardIdOld) {


        var cardId = this.arrCardOld[cardIdOld];
        var rank =  Math.floor(cardId/ 4) + 1;
        var suit = cardId%4;
      
        return {rank:rank,suit:suit};
    

    
    }


    getArrCardObject(arrID){
        var arr = [];
        arrID.forEach(element => {
            arr.push(this.getCardWithId(element));
        });
        return arr;
    }

    addCardsNode(cards: any,animation){
        if(cards == undefined){
            return;
        }
        console.log("addCardsNode");
        var thiz = this;
        
        var tranfromCard = cards[0].node;
        var cardWith = 84;
        var cardHeight = 120;
        this.removeAll();
       
        var distanceX = (this.node.getComponent(UITransform).width - cardWith ) / (cards.length-1);
        if (distanceX > cardWith) {
            distanceX = cardWith;
        }
        var distanceY = 0.33*cardHeight;
       // var yyyy = (this.node.getComponent(UITransform).contentSize.height - 2/3*cardHeight  ) / (cards.length-1);
        // if (distanceY > yyyy) {
        //     distanceY = yyyy;
        // }

        if(this.playeviewType == PlayviewType.OTHER){
            distanceX = 1/3*cardWith;
        }
        var y = thiz.height / 2;

        for (var i = 0; i < cards.length; i++) {

            // this.reorderChild(this.cardList[i], i);

            (()=>{
                var inew  = i;
             
                var card =cards [inew];
                var cardNode = card.node;
                thiz.cardList.push(card);
                
                var posWoar = card.node.getParent().getComponent(UITransform).convertToWorldSpaceAR(card.node.getPosition());
                console.log("posWourld"+ posWoar);
                var pold = thiz.node.getComponent(UITransform).convertToNodeSpaceAR(posWoar);
                console.log("posLocasl"+ pold);
                 cardNode.parent =  thiz.node;
                
                cardNode.setPosition(pold);

                card.cardDistance = distanceX;
                cardNode.setContentSize(size(cardWith,cardHeight));
                var posOrigin = new Vec3(inew * distanceX + cardWith / 2, y, 0);
                if(thiz.anchorType == AnchorType.CENTER){
                    posOrigin = v3( inew * distanceX + cardWith/2 - ( distanceX*cards.length + 1/3*cardWith)/2 ,posOrigin.y,0);
                    
                }

                if(thiz.direction == DirectionType.VERTICAL){
                    posOrigin = new Vec3(thiz.width/2, inew * distanceY, 0);
                    cardNode.setContentSize(size(thiz.width,cardHeight));
                    if(thiz.anchorType == AnchorType.CENTER){
                        posOrigin = new Vec3(thiz.width/2, -inew * distanceY - cardHeight/2 + ( distanceY*cards.length + (1-0.33)*cardHeight)/2, 0);
                        
                    }
    
                }

                if (animation && !thiz.isbackground) {
                    
                    Tween.stopAllByTarget(card.node);
                    tween(card.node).to(0.25, { position: posOrigin }).start();
    
    
                }
                else {
                    card.node.setPosition(posOrigin);
                }
               
               
            })();
           


        }
    }

    addCards(cards: any,isReconnectt,nodeFrom){
        var thiz = this;
        var animation = false;
       
        if(nodeFrom != null){
            animation = true;
        }
        if(isReconnectt){
            animation = false;
        }
        var cardClone =  this.cardClone;
        var tranfromCard = cardClone;
        var cardWith = 84;
        var cardHeight = 120;
        // var cardHeight = this.width * tranfromCard.contentSize.height /tranfromCard.contentSize.width;
        this.removeAll();
       
        var distanceX = (this.node.getComponent(UITransform).width - cardWith ) / (cards.length-1);
        if (distanceX > cardWith) {
            distanceX = cardWith;
        }
        var distanceY = 0.33*cardHeight;
       // var yyyy = (this.node.getComponent(UITransform).contentSize.height - 2/3*cardHeight  ) / (cards.length-1);
        // if (distanceY > yyyy) {
        //     distanceY = yyyy;
        // }

        if(this.playeviewType == PlayviewType.OTHER){
            distanceX = 1/3*cardWith;
        }
        var y = thiz.height / 2;
        let startX = 0 - cardWith / 2;
        for (var i = 0; i < cards.length; i++) {

            // this.reorderChild(this.cardList[i], i);

            (()=>{
                var inew  = i;
                var cardNode = instantiate(cardClone);
                var card = cardNode.getComponent(Card);
                thiz.cardList.push(card);
                cardNode.parent =  thiz.node;

               
                card.cardDistance = distanceX;
                cardNode.getComponent(UITransform).setContentSize(size(cardWith,cardHeight));
                var posOrigin = new Vec3(inew * distanceX + cardWith / 2, y, 0);
                if(thiz.anchorType == AnchorType.CENTER){
                    posOrigin = v3( inew * distanceX + cardWith/2 - ( distanceX*cards.length + 1/3*cardWith)/2 ,posOrigin.y,0);
                    
                }

                if(thiz.anchorType == AnchorType.RIGTH){

                    let posX = startX - i * distanceX;
                    posOrigin = v3(posX, y, 0);
                    // posOrigin = v3( -inew * distanceX + cardWith / 2 ,y,0);
                    cardNode.setSiblingIndex(-(inew+1));

                }

                if(thiz.direction == DirectionType.VERTICAL){
                    posOrigin = new Vec3(thiz.width/2, inew * distanceY, 0);
                    cardNode.getComponent(UITransform).setContentSize(size(thiz.width,cardHeight));
                    if(thiz.anchorType == AnchorType.CENTER){
                        posOrigin = new Vec3(thiz.width/2, -inew * distanceY - cardHeight/2 + ( distanceY*cards.length + (1-0.33)*cardHeight)/2, 0);
                        
                    }
    
                }
                card.initCard(thiz.getCardWithId(cards[inew]),cards[inew],thiz,posOrigin,this.canTouch,inew);
                card.updateFrameCard();
                card.node.active = true;
                if (animation) {
                    var posWoar = nodeFrom.getParent().getComponent(UITransform).convertToWorldSpaceAR(nodeFrom.getPosition());
                    var pold = thiz.node.getComponent(UITransform).convertToNodeSpaceAR(posWoar);
                    cardNode.setPosition(pold);
                   
                    Tween.stopAllByTarget(card.node);
                    tween(card.node).to(0.25, { position: posOrigin }).start();
    
    
                }
                else {
                    card.node.setPosition(posOrigin);
                }
               
                card.node.setPosition(posOrigin);
               
            })();
           


        }
    }

    // update (dt) {}
}

