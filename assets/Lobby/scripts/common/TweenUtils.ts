
import { _decorator, Component, Node, Label, director } from 'cc';
import { Utils } from './Utils';

const { ccclass, property } = _decorator;

export class TweenListener {
    target: Component = null;
    duration: number = 0;
    curDuration: number = 0;
    callback: (p: number) => void = null;
}

@ccclass('TweenUtils')
export class TweenUtils extends Component {

    private static instance: TweenUtils = null;
    private static getInstance(): TweenUtils {
        if (this.instance == null) {
            let node = new Node("Tween");
            director.addPersistRootNode(node);
            this.instance = node.addComponent(TweenUtils);
        }
        return this.instance;
    }

    private static listeners = new Array<TweenListener>();

    private skeepFrame = false;
    private readonly countSkeep = 1;
    private curCountSkeep = 0;
    private delta = 0;

    update(dt: number) {
        if (this.skeepFrame) {
            this.curCountSkeep++;
            this.delta += dt;
            if (this.curCountSkeep >= this.countSkeep) {
                this.curCountSkeep = 0;
                this.skeepFrame = false;
            }
            return;
        }
        for (var i = 0; i < TweenUtils.listeners.length; i++) {
            let listener = TweenUtils.listeners[i];
            if (listener.target && listener.target instanceof Component && listener.target.node) {
                listener.curDuration = Math.min(listener.duration, listener.curDuration + dt + this.delta);
                listener.callback(listener.curDuration / listener.duration);
                if (listener.curDuration >= listener.duration) {
                    TweenUtils.listeners.splice(i--, 1);
                }
            } else {
                TweenUtils.listeners.splice(i--, 1);
            }
        }

        this.skeepFrame = true;
        this.delta = 0;
    }

    static numberTo(label: Label, toNumber: number, duration: number, format: (n: number) => string = (n) => { return Utils.formatNumber(n) }) {
        this.getInstance();
        let listener = null;
        for (var i = 0; i < TweenUtils.listeners.length; i++) {
            let _listener = TweenUtils.listeners[i];
            if (_listener.target == label) {
                listener = _listener;
                break;
            }
        }
        if (listener == null) {
            listener = new TweenListener();
            this.listeners.push(listener);
        }
        let startNumber = parseInt(label.string.replace(/\./g, "")) || 0;//Utils.stringToInt(label.string);
        let distance = toNumber - startNumber;
        listener.curDuration = 0;
        listener.duration = duration;
        listener.target = label;
        listener.callback = (p: number) => {
            label.string = format(parseInt("" + (startNumber + distance * p)));
        }
    }
}