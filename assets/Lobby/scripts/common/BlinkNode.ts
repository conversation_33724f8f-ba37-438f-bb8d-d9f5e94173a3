import { _decorator, Component, UIOpacity, Node, math } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('BlinkNode')
export default class BlinkNode extends Component {

    @property
    blinkSpeed: number = 4;

    private blinkTime: number = 0;
    private uiOpacity: UIOpacity | null = null;

    onLoad() {
        // Đảm bảo node có UIOpacity component
        this.uiOpacity = this.node.getComponent(UIOpacity);
        if (!this.uiOpacity) {
            this.uiOpacity = this.node.addComponent(UIOpacity);
        }
    }

    update(dt: number) {
        this.blinkTime += dt * this.blinkSpeed * 2 * Math.PI;
        const visible = Math.sin(this.blinkTime) > 0;

        if (this.uiOpacity) {
            this.uiOpacity.opacity = visible ? 180 : 20;
        }
    }
}
