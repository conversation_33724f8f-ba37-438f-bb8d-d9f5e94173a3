import Configs from "../Config";
import SignalRClient from "./Network.SignalRClient";

export default class DQSignalRClient extends SignalRClient {
    public static instance: DQSignalRClient = null;

    public static getInstance(): DQSignalRClient {
        if (this.instance == null) {
            this.instance = new DQSignalRClient();
        }
        return this.instance;
    }

    onSuccess: Function;

    public onstart() {
        this.onSuccess(true);
    }

    public onError() {
        this.onSuccess(false);
    }

    public connectHub(onSuccess: Function) {
        this.onSuccess = onSuccess;
        super.connect(Configs.App.DOMAIN_CONFIG['DancingHubUrl']);
    }
}

