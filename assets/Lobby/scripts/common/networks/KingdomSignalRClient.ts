import Configs from "../Config";
import SignalRClient from "./Network.SignalRClient";

export default class KingdomSignalRClient extends SignalRClient {
    public static instance: KingdomSignalRClient = null;

    public static getInstance(): KingdomSignalRClient {
        if (this.instance == null) {
            this.instance = new KingdomSignalRClient();
        }
        return this.instance;
    }

    onSuccess: Function;

    public onstart() {
        this.onSuccess(true);
    }

    public onError() {
        this.onSuccess(false);
    }

    public connectHub(onSuccess: Function) {
        this.onSuccess = onSuccess;
        super.connect(Configs.App.DOMAIN_CONFIG['KingdomHubUrl']);
    }
}

