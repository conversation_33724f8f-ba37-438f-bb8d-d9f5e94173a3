import Configs from "../Config";
import SignalRClient from "./Network.SignalRClient";

export default class OracleSignalRClient extends SignalRClient {
    public static instance: OracleSignalRClient = null;

    public static getInstance(): OracleSignalRClient {
        if (this.instance == null) {
            this.instance = new OracleSignalRClient();
        }
        return this.instance;
    }

    onSuccess: Function;

    public onstart() {
        this.onSuccess(true);
    }

    public onError() {
        this.onSuccess(false);
    }

    public connectHub(onSuccess: Function) {
        this.onSuccess = onSuccess;
        super.connect(Configs.App.DOMAIN_CONFIG['OlympiaHubUrl']);
    }
}