import Configs from "../Config";
import SignalRClient from "./Network.SignalRClient";

export default class ForestSignalRClient extends SignalRClient {
    public static instance: ForestSignalRClient = null;

    public static getInstance(): ForestSignalRClient {
        if (this.instance == null) {
            this.instance = new ForestSignalRClient();
        }
        return this.instance;
    }

    onSuccess: Function;

    public onstart() {
        this.onSuccess(true);
    }

    public onError() {
        this.onSuccess(false);
    }

    public connectHub(onSuccess: Function) {
        this.onSuccess = onSuccess;
        super.connect(Configs.App.DOMAIN_CONFIG['ForestHubUrl']);
    }
}

