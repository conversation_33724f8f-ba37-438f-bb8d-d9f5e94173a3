import Configs from "../Config";
import SignalRClient from "./Network.SignalRClient";


export default class CaiShenSignalRClient extends SignalRClient {
    public static instance: CaiShenSignalRClient = null;

    public static getInstance(): CaiShenSignalRClient {
        if (this.instance == null) {
            this.instance = new CaiShenSignalRClient();
        }
        return this.instance;
    }

    onSuccess: Function;

    public onstart() {
        this.onSuccess(true);
    }

    public onError() {
        this.onSuccess(false);
    }

    public connectHub(onSuccess: Function) {
        this.onSuccess = onSuccess;
        super.connect(Configs.App.DOMAIN_CONFIG['GodOfFortuneHubUrl']);
    }
}