import Configs from "../Config";
import SignalRClient from "./Network.SignalRClient";

export default class OceanSignalRClient extends SignalRClient {
    public static instance: OceanSignalRClient = null;

    public static getInstance(): OceanSignalRClient {
        if (this.instance == null) {
            this.instance = new OceanSignalRClient();
        }
        return this.instance;
    }

    onSuccess: Function;

    public onstart() {
        this.onSuccess(true);
    }

    public onError() {
        this.onSuccess(false);
    }

    public connectHub(onSuccess: Function) {
        this.onSuccess = onSuccess;
        super.connect(Configs.App.DOMAIN_CONFIG['OceanHubUrl']);
    }
}