import { _decorator, Component, Node, Sprite, SpriteFrame } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('EventIcon')
export class EventIcon extends Component {
    @property(SpriteFrame)
    iconX6: SpriteFrame = null;

    @property(SpriteFrame)
    iconX5: Sprite<PERSON>rame = null;

    @property(SpriteFrame)
    iconX4: Sprite<PERSON>rame = null;

    @property(SpriteFrame)
    iconX3: SpriteFrame = null;

    @property(SpriteFrame)
    iconX2: SpriteFrame = null;

    protected onLoad(): void {
        this.node.getComponent(Sprite).spriteFrame = null;
    }

    setIcon(multiplier: number, nextJackpot: number) {
        if (nextJackpot === 0) {
            switch (multiplier) {
                case 2:
                    this.node.getComponent(Sprite).spriteFrame = this.iconX2;
                    break;
                case 3:
                    this.node.getComponent(Sprite).spriteFrame = this.iconX3;
                    break;
                case 4:
                    this.node.getComponent(Sprite).spriteFrame = this.iconX4;
                    break;
                case 5:
                    this.node.getComponent(Sprite).spriteFrame = this.iconX5;
                    break;
                case 6:
                    this.node.getComponent(Sprite).spriteFrame = this.iconX6;
                    break;
            }
            return;
        }
        this.node.getComponent(Sprite).spriteFrame = null;
    }
}

