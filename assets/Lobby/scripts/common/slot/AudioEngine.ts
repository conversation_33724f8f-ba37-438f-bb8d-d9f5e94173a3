import { _decorator, AudioClip, AudioSource, Node } from 'cc';
const { ccclass } = _decorator;

@ccclass('AudioEngine')
export class AudioEngine {
    private static _instance: AudioEngine;

    private _bgmSource: AudioSource | null = null;
    private _sfxSources: Map<number, AudioSource> = new Map();
    private _sfxIdCounter = 0;

    private constructor() {}

    public static getInstance(): AudioEngine {
        if (!this._instance) {
            this._instance = new AudioEngine();
        }
        return this._instance;
    }

    /** Initialize with an AudioSource for BGM */
    public init(bgmSource: AudioSource) {
        this._bgmSource = bgmSource;
    }

    /** Play background music */
    public playMusic(clip: AudioClip, loop: boolean = true, volume: number = 1): void {
        if (!this._bgmSource) {
            console.warn('[AudioManager] BGM AudioSource not initialized');
            return;
        }
        this._bgmSource.clip = clip;
        this._bgmSource.loop = loop;
        this._bgmSource.volume = volume;
        this._bgmSource.play();
    }

    public stopMusic(): void {
        this._bgmSource?.stop();
    }

    public pauseMusic(): void {
        this._bgmSource?.pause();
    }

    public resumeMusic(): void {
        this._bgmSource?.play();
    }

    public setMusicVolume(volume: number): void {
        if (this._bgmSource) {
            this._bgmSource.volume = volume;
        }
    }

    public isMusicPlaying(): boolean {
        return this._bgmSource?.playing ?? false;
    }

    /** Play sound effect and return an ID to track it */
    public playEffect(clip: AudioClip, loop: boolean = false, volume: number = 1): number {
        const node = new Node(`SFX_${this._sfxIdCounter}`);
        const audioSource = node.addComponent(AudioSource);
        audioSource.clip = clip;
        audioSource.loop = loop;
        audioSource.volume = volume;
        audioSource.play();

        const id = this._sfxIdCounter++;
        this._sfxSources.set(id, audioSource);

        audioSource.node.once(AudioSource.EventType.ENDED, () => {
            this._sfxSources.delete(id);
            node.destroy();
        });

        return id;
    }

    public stopEffect(id: number): void {
        const source = this._sfxSources.get(id);
        if (source) {
            source.stop();
            source.node.destroy();
            this._sfxSources.delete(id);
        }
    }

    public stopAllEffects(): void {
        for (const [id, source] of this._sfxSources) {
            source.stop();
            source.node.destroy();
        }
        this._sfxSources.clear();
    }

    public setEffectsVolume(volume: number): void {
        for (const source of this._sfxSources.values()) {
            source.volume = volume;
        }
    }

    public pauseAllEffects(): void {
        for (const source of this._sfxSources.values()) {
            source.pause();
        }
    }

    public resumeAllEffects(): void {
        for (const source of this._sfxSources.values()) {
            source.play();
        }
    }
}
