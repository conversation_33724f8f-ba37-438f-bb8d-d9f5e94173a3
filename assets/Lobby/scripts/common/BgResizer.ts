
import {
    _decorator,
    Component,
    instantiate,
    log,
    Node,
    view,
    ResolutionPolicy,
    sys,
    UIOpacity,
    game,
    Label,
    AudioSource,
    System,
    Tween,
    tween,
    v3,
    easing,
    Animation,
    random,
    Toggle,
    Prefab,
    assetManager,
    ImageAsset,
    SpriteFrame,
    Texture2D,
    Sprite,
    Widget,
    Color,
    Vec3,
    <PERSON><PERSON><PERSON>,
    director,
    Size,
    size,
    UITransform
} from "cc";
const {ccclass, property} = _decorator;
@ccclass
export default class BgResizer extends Component {
    @property
    designResolution: Size = new Size(1920, 1080);

    lastWitdh: number = 0;
    lastHeight: number = 0;

    start() {
        this.updateSize();
    }

    update(dt: number) {
        this.updateSize();
    }

    updateSize() {
        var frameSize = view.getFrameSize();
        if (this.lastWitdh !== frameSize.width || this.lastHeight !== frameSize.height) {

            this.lastWitdh = frameSize.width;
            this.lastHeight = frameSize.height;

            if (this.designResolution.width / this.designResolution.height > frameSize.width / frameSize.height) {
                var height = this.designResolution.width * frameSize.height / frameSize.width;
                var width = height * this.designResolution.width / this.designResolution.height;
                
                var newDesignSize = size(width, height);
                this.getComponent(UITransform).setContentSize(newDesignSize);
            } else {
                var width = this.designResolution.height * frameSize.width / frameSize.height;
                var height = width * this.designResolution.height / this.designResolution.width;
                var newDesignSize = size(width, height);

                var newDesignSize = size(width, height);
                this.node.getComponent(UITransform).setContentSize(newDesignSize);
            }
        }
    }
}
