// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import { _decorator, Component, Label, log, Slider, Sprite, Toggle } from "cc";
import { Utils } from "./Utils";
import Configs from "./Config";
import CardGameSignalRClient from "./networks/CardGameSignalRClient";
import App from "./App";




const {ccclass, property} = _decorator;

@ccclass
export default class BuyChipPoker extends Component {

    @property(Label)
    lblMin: Label = null;
    @property(Label)
    lblMax: Label = null;

    @property(Label)
    lblMoneyMe: Label = null;

    @property(Label)
    lblMoneyBuy: Label = null;

    
    @property(Toggle)
    togAuto: Toggle = null;
    // @property
    // text: string = 'hello';

    // LIFE-CYCLE CALLBACKS:

    // onLoad () {}
    @property(Slider)
    slider: Slider = null!; 

    @property(Sprite)
    bgSlide: Sprite = null!; 

    start () {
    
    }

    moneyBuy = 0;
    minBet = 0;
    moneyMin = 0;
    moneyMax = 0;
    CurrencyID = 0;

    isFromLobby = false;
    showWithData(money,isVip,isFromLobby){
        this.isFromLobby = isFromLobby;
        this.CurrencyID = isVip;
        this.node.active = true;
        this.moneyMin = money*20;
        this.moneyMax = money*200;
        this.minBet = money;
        this.lblMin.string = Utils.formatNumber(this.moneyMin);
        this.lblMax.string =  Utils.formatNumber(this.moneyMax);
        this.slider.progress = 0.5;
        this.lblMoneyMe.string = Utils.formatNumber(isVip==1?Configs.Login.GoldBalance:Configs.Login.CoinBalance);
        this.bgSlide.fillRange = this.slider.progress;
       this.updateMoneyBuy();
        
        
    }
    onSliderChanged(event: Event) {
     
        // Print the slider's percentage (0-100)
        this.bgSlide.fillRange = this.slider.progress;
       this.updateMoneyBuy();
    }

    updateMoneyBuy(){
        this.moneyBuy = this.moneyMin + Math.floor( this.slider.progress*(this.moneyMax-this.moneyMin));
        this.lblMoneyBuy.string = Utils.formatNumber(this.moneyBuy);
    }

    actCancel(){
        this.node.active = false;
    }
    actDongY(){
        var thiz = this;
        if(this.isFromLobby){
            CardGameSignalRClient.getInstance().send('PlayNow', [this.minBet,this.CurrencyID,this.moneyBuy,this.togAuto.isChecked], (data) => {
                log(data);
                if(data == -1){
                  App.instance.ShowAlertDialog(App.instance.getTextLang("st12")) ;
                }else{
                    thiz.node.active = false;
                }
                       });
        }else{

            var zzz = [this.minBet,this.CurrencyID,this.moneyBuy,this.togAuto.isChecked];
            log("mua chip"+zzz);
            CardGameSignalRClient.getInstance().send('BuyIn', [this.minBet,this.CurrencyID,this.moneyBuy,this.togAuto.isChecked], (data) => {
                log(data);
                if(data == -1){
                  App.instance.ShowAlertDialog(App.instance.getTextLang("st12")) ;
                }else{
                    thiz.node.active = false;
                }
                       });
        }
      
    }

    actCong(){
        var max = this.slider.progress+0.2;
        if(max>1){
            max = 1;
        }
        this.slider.progress = max;
        this.bgSlide.fillRange = max;
        this.updateMoneyBuy();
    }

    actTru(){
        var max = this.slider.progress-0.2;
        if(max<0){
            max = 0;
        }
        this.slider.progress = max;
        this.bgSlide.fillRange = max;
        this.updateMoneyBuy();
    }

    // update (dt) {}
}
