// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import CartList from "./CardList";

const {ccclass, property} = cc._decorator;

@ccclass
export default class CardOnTable extends CartList {

    cardList = [];
    arrr:any = null;
    isReconnectt: any;
    isNode:boolean;
    fromNode:null;
     start() {
       super.start();
       this.node.setContentSize(cc.size(500,120));
       if(!this.arrr) return;
       if(this.isNode){
         this.addCardsNode(this.arrr,!this.isReconnectt);
       }else{
         this.addCards(this.arrr,this.isReconnectt,this.fromNode);
       }
     }
 
     addCardOntable(arr,isReconnect,_isNode,fromNode){
       
         this.arrr = arr;
         this.isReconnectt = isReconnect;
         this.isNode = _isNode;
         this.fromNode = fromNode;
     }
 
     deactiveCard(){
       this.cardList.forEach(element => {
        cc.log("mo card");
         element.node.color = cc.color(127, 127, 127,255);
       });
     }
 
     update(deltaTime: number) {
         
     }
}
