import { _decorator, Component, tween, Vec3 } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('RotateSmooth')
export default class RotateSmooth extends Component {
    @property
    stepAngle: number = 15; // Góc quay mỗi bước (độ)

    @property
    stepDuration: number = 0.2; // Thời gian xoay mỗi bước (giây)

    @property
    delayBetweenSteps: number = 0.2; // Thời gian nghỉ giữa các bước (giây)

    onLoad() {
        this.startSteppedRotation();
    }

    startSteppedRotation() {
        const node = this.node;
        const stepAngle = this.stepAngle;
        const stepDuration = this.stepDuration;
        const delay = this.delayBetweenSteps;

        // Lặp xoay vô hạn từng bước bằng tween đệ quy
        const rotateStep = () => {
            tween(node)
                .by(stepDuration, { eulerAngles: new Vec3(0, 0, stepAngle) })
                .delay(delay)
                .call(() => {
                    rotateStep(); // lặp lại
                })
                .start();
        };

        rotateStep(); // bắt đầu xoay
    }
}
