import {_decorator, Component, Node, Sprite, SpriteFrame, tween, UIOpacity, CCInteger} from 'cc';

const {ccclass, property} = _decorator;

@ccclass
export default class BannerSlider extends Component {
    @property([Node])
    banners: Node[] = [];
    @property([Sprite])
    dots: Sprite[] = [];
    @property(SpriteFrame)
    dotActive: SpriteFrame = null;
    @property(SpriteFrame)
    dotInactive: SpriteFrame = null;
    @property(CCInteger)
    interval: number = 3;

    private currentIndex: number = 0;

    onLoad() {
        this.schedule(this.nextSlide, this.interval);
        this.updateSlide();
    }

    nextSlide() {
        this.currentIndex = (this.currentIndex + 1) % this.banners.length;
        this.updateSlide();
    }

    updateSlide() {
        for (let i = 0; i < this.banners.length; i++) {
            const banner = this.banners[i];
            const isCurrent = (i === this.currentIndex);

            let opacityComp = banner.getComponent(UIOpacity);
            if (!opacityComp) {
                opacityComp = banner.addComponent(UIOpacity);
            }

            opacityComp.opacity = isCurrent ? 0 : 255;
            banner.active = true;

            tween(opacityComp)
                .to(0.5, {opacity: isCurrent ? 255 : 0})
                .call(() => {
                    if (!isCurrent) banner.active = false;
                })
                .start();

            this.dots[i].spriteFrame = isCurrent ? this.dotActive : this.dotInactive;
        }
    }
}
