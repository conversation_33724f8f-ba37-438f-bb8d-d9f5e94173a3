// // Learn TypeScript:
// //  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// // Learn Attribute:
// //  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// // Learn life-cycle callbacks:
// //  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

// import Utils from "../LobbyScript/Script/common/Utils";

// const {ccclass, property} = cc._decorator;

// @ccclass
// export default class NewClass extends cc.Component {
//     @property(cc.Label)
//     lblName: cc.Label;

//     @property(cc.Label)
//     lblGold: cc.Label;


//     @property(cc.Sprite)
//     timeSprite: cc.Sprite;

//     fillRange: number = 1;

//     timeMax:number = 30;

//     isStartTime: boolean = false;

//     player:any;

//     @property(cc.Node)
//     spOwner: cc.Node;

//     onLoad(){
       
//         this.lblName.useSystemFont = false;
//         // this.lblName.font = App.instance.linkCard.fontName;

//         this.lblGold.useSystemFont = false;
//         // this.lblGold.font = App.instance.linkCard.fontGold;
//         this.updateInfor(null);
//         this.startCountDown(0);
//     }

//     start() {
       
//         console.log("start Playeview");
//     }

    
//     updateInfor(_data) {
      
//         //
//     }




//     // update (dt) {}
// }
