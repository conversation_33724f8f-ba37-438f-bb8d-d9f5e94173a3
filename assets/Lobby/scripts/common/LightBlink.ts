// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import { _decorator, Component ,Node} from "cc";

const {ccclass, property} = _decorator;

@ccclass
export default class NewClass extends Component {

     @property(Node)
    lightsGroup1: Node = null;

    @property(Node)
    lightsGroup2: Node = null;

    @property
    blinkInterval: number = 0.25; // thời gian đổi đèn (giây)

    private currentGroup = 1;

    onLoad() {
        this.schedule(this.toggleLights, this.blinkInterval);
    }

    toggleLights() {
        const active1 = this.currentGroup === 1;
        this.lightsGroup1.active = active1;
        this.lightsGroup2.active = !active1;

        this.currentGroup = active1 ? 2 : 1;
    }

    // update (dt) {}
}
