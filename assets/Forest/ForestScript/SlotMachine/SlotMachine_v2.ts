import { _decorator, Node } from 'cc';
import { BaseSlotMachine } from "../../../Lobby/scripts/common/slot/BaseSlotMachine";
import { Utils } from "../../../Lobby/scripts/common/Utils";
import { ForestAudioManager, AUDIO_CLIP } from "../ForestAudioManager";
import ForestPayLines from "./ForestPayLines";
import { SlotSymbol_v2 } from "./SlotSymbol_v2";

const {ccclass, property} = _decorator;


const TURBO_CONFIG = {
    elasticPercent: 35,
    symbolOffset: 24,
    spinDuration: 1.5,
    delayReel: 0.1,
    rowCount: 3,
};

const NORMAL_CONFIG = {
    elasticPercent: 35,
    symbolOffset: 24,
    spinDuration: 2.5,
    delayReel: 0.25,
    rowCount: 3,
};


@ccclass
export class SlotMachine_v2 extends BaseSlotMachine {

    @property(ForestPayLines)
    private payLines: ForestPayLines = null;

    protected onLoad(): void {
        this.listeners = {
            onReelCompleted: () => {
                ForestAudioManager.Instance.playEffect(AUDIO_CLIP.END_REEL_SPIN);
            }
        }
    }

    protected initializeSymbol(symbolNode: Node, isBlur: boolean): void {
        let sID = `${Utils.randomRangeInt(1, 8)}`
        symbolNode.getComponent(SlotSymbol_v2).setIsBlur(isBlur).setId(sID).show();
    }

    override async highlightItems(indexes: number[], prizeLines?: string[]): Promise<void> {
        await this.payLines.showPayLinesAnimation(prizeLines);
        await super.highlightItems(indexes, prizeLines);
    }

    override async resetAllItems(): Promise<void> {
        super.resetAllItems();
        this.payLines.resetAllPayLines();
    }

    public setTurbo(isTurbo: boolean){
        this.setConfig(isTurbo ? TURBO_CONFIG : NORMAL_CONFIG);
    }
    
}