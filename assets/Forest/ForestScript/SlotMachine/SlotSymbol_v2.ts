import { _decorator, Animation } from 'cc';
import { BaseSlotSymbol, SymbolState } from "../../../Lobby/scripts/common/slot/BaseSlotSymbol";
const { ccclass, property } = _decorator;

@ccclass
export class SlotSymbol_v2 extends BaseSlotSymbol {

    protected onLoad(): void {
        this.setStateHandler({
            [SymbolState.INIT]: async () => {
                this.symbol.node.active = true;
                this.defaultAnimation.node.active = false;
                return Promise.resolve();
            },
            [SymbolState.HIDE]: async () => {
                return Promise.resolve();
            },
            [SymbolState.HIGHLIGHT]: async () => {
                return new Promise<void>((resolve) => {
                    this.symbol.node.active = false;
                    this.defaultAnimation.node.active = true;
                    const animationName = "Item" + this.getId();
                    this.defaultAnimation.play(animationName);
                    this.defaultAnimation.once(Animation.EventType.FINISHED, () => {
                        resolve();
                    })
                })
            }
        })
    }
    
}
