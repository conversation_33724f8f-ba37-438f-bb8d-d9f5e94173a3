import { _decorator, Node, Material, Toggle, Sprite } from 'cc';
import Dialog from "../../Lobby/scripts/common/Dialog";

const {ccclass, property} = _decorator;

@ccclass
export default class ForestPopupSelectLine extends Dialog {
    @property(Node)
    private buttonLines: Node = null;

    @property(Material)
    normalMaterial: Material = null;

    @property(Material)
    grayScaleMaterial: Material = null;

    private _onSelectedChanged: (selectedLines: number[]) => void = null;

    show() {
        super.show();
    }

    dismiss() {
        super.dismiss();
    }

    _onShowed() {
        super._onShowed();
    }

    public setOnSelectedChanged(fn: (selectedLines: number[]) => void) {
        this._onSelectedChanged = fn;
    }

    protected onLoad() {
        this.buttonLines.children.forEach((btn, index) => {
            btn.on("toggle", () => this.updateSelectedLines());
        });
    }

    private updateSelectedLines() {
        const selectedLines = this.getSelectedLines();
        this.buttonLines.children.forEach(btn => {
            const toggle = btn.getComponent(Toggle);
            const sprite = btn.getComponent(Sprite);
            sprite.material = toggle.isChecked ? this.normalMaterial : this.grayScaleMaterial;
        });
        this._onSelectedChanged?.(selectedLines);
    }

    private toggleLines(condition: (lineNumber: number) => boolean) {
        this.buttonLines.children.forEach((btn, index) => {
            const lineNumber = index + 1;
            btn.getComponent(Toggle).isChecked = condition(lineNumber);
        });
        this.updateSelectedLines();
    }

    activeEvenLines() {
        this.toggleLines(lineNumber => lineNumber % 2 === 0);
    }

    activeOddLines() {
        this.toggleLines(lineNumber => lineNumber % 2 !== 0);
    }

    activeAllLines() {
        this.toggleLines(() => true);
    }

    deactiveAllLines() {
        this.toggleLines(() => false);
    }

    public getSelectedLines(): number[] {
        const selectedLines: number[] = [];
        this.buttonLines.children.forEach((btn, index) => {
            if (btn.getComponent(Toggle).isChecked) {
                selectedLines.push(index + 1);
            }
        });
        return selectedLines;
    }
}
