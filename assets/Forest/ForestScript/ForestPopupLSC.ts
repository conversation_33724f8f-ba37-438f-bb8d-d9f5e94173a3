import { _decorator, Button, Node, Label, instantiate, error } from 'cc';
import App from "../../Lobby/scripts/common/App";
import Http from "../../Lobby/scripts/common/Http";
import Configs from "../../Lobby/scripts/common/Config";
import { Utils } from "../../Lobby/scripts/common/Utils";
import Dialog from "../../Lobby/scripts/common/Dialog";

const {ccclass, property, menu} = _decorator;

@ccclass
@menu('Forest/PopupLSC')
export default class ForestPopupLSC extends Dialog {
    @property(Button) prevPageButton: Button = null;
    @property(Button) nextPageButton: Button = null;
    @property(Node) rowTemplate: Node = null;
    @property(Node) rowContainer: Node = null;

    private currentPage: number = 1;
    private pageSize: number = 10;
    private totalRows: number = 0;

    show() {
        super.show();
        this.currentPage = 1;
    }

    dismiss() {
        super.dismiss();
    }

    _onShowed() {
        super._onShowed();
        this.rowContainer.removeAllChildren();
        this.loadData().then();
    }

    private async loadData() {
        App.instance.showLoading(true);
        try {
            const result = await this.fetchData();
            this.displayData(result);
        } catch (e) {
            error(e);
        } finally {
            App.instance.showLoading(false);
        }
    }

    private fetchData(): Promise<any[]> {
        return new Promise((resolve, reject) => {
            Http.get(Configs.App.DOMAIN_CONFIG['Forest_GetTransactionLog'], {
                "CurrencyID": Configs.Login.CurrencyID,
                "type": 1,
                "Page": this.currentPage,
                "PageSize": this.pageSize,
            }, (status, response) => {
                if (status === 200) {
                    this.currentPage = response["p"][0]; // Update current page
                    this.pageSize = response["p"][1]; // Update page size
                    this.totalRows = response["p"][2]; // Update total rows
                    this.updatePagination();
                    resolve(response["d"]);
                } else {
                    reject(new Error("Fetching failed"));
                }
            })
        })
    }

    private displayData(result: any[]) {
        this.rowContainer.removeAllChildren();
        for (let i = 0; i < result.length; i++) {
            let itemRow = instantiate(this.rowTemplate);
            itemRow.active = true;
            this.rowContainer.addChild(itemRow);

            let winLineLength = result[i].prizeData ? result[i].prizeData.split(";").length : 0;

            itemRow.children[0].getComponent(Label).string = result[i].spinID;
            itemRow.children[1].getComponent(Label).string = Utils.formatDatetime(result[i].createTime, "dd-MM-yyyy\nHH:mm:ss");
            itemRow.children[2].getComponent(Label).string = result[i].betValue;
            itemRow.children[3].getComponent(Label).string = Utils.formatNumber(result[i].totalBetValue);
            itemRow.children[4].getComponent(Label).string = `${winLineLength} ${App.instance.getTextLang("mn12")}`;
            itemRow.children[5].getComponent(Label).string = Utils.formatNumber(result[i].paylinePrizeValue);

        }
    }

    updatePagination() {
        const totalPages = Math.ceil(this.totalRows / this.pageSize);

        this.nextPageButton.node.active = this.currentPage < totalPages;
        this.prevPageButton.node.active = this.currentPage > 1;
    }

    onClickNextPage() {
        let totalPages = Math.ceil(this.totalRows / this.pageSize);
        if (this.currentPage < totalPages) {
            this.currentPage++;
            this.loadData().then();
        }
    }

    onClickPrevPage() {
        if (this.currentPage > 1) {
            this.currentPage--;
            this.loadData().then();
        }
    }

}
