import { _decorator, Component, Node, Label, Button, Animation, tween, Tween, easing, UIOpacity } from 'cc';
import { ForestAudioManager, AUDIO_CLIP } from "./ForestAudioManager";
import { TweenUtils } from '../../Lobby/scripts/common/TweenUtils';
import App from '../../Lobby/scripts/common/App';

const { ccclass, property } = _decorator;

enum eANIMATION {
    WORM = 'Worm',
    EXPLOSION = 'Explosion',
    BARREL = 'Barrel',
    BUTTERFLY = 'Butterfly',
    YELLOW_FLOWER = 'YellowFlower',
}

@ccclass
export default class ForestPopupBonus extends Component {
    @property(Node) items: Node = null;
    @property(Label) lblTurn: Label = null;
    @property(Label) lblDiem: Label = null;
    @property(Label) lblHeso: Label = null;

    @property(Node) popout: Node = null;
    @property(Node) quickPlay: Node = null;
    @property(Label) lblCountDownTime: Label = null;

    public onFinishProcess: Function = null;
    private bonusData: Array<[number, number, number, number]> = [];
    private left: number;
    private _point: number;
    private countdownInterval: number = null;
    private _totalTurn = 0;
    private startBonus = 0;
    private time = 0;

    private set totalTurn(value: number) {
        this._totalTurn = value;
        this.lblTurn.string = `${value}`;
    }

    private get totalTurn() {
        return this._totalTurn;
    }

    
    private set point(value: number) {
        this._point = value;
        this.lblDiem.string = `${value}`;
    }

    private get point() {
        return this._point;
    }

    protected onLoad() {
        for (let i = 0; i < this.items.children.length; i++) {
            let node = this.items.children[i];
            node.on("click", () => {
                this.quickPlay.active = false;
                this.stopCountdown();
                if (node["_isOpen"] === false && this.left > 0) {
                    const [step, rewardID, multiplier, price] = this.bonusData[this.bonusData.length - this.left];
                    node["_isOpen"] = true;
                    let animation: Animation = node["_animation"];
                    let prizeValue: Label = node["_price"];
                    const duration = animation.getState(eANIMATION.EXPLOSION).duration;
                    switch (rewardID) {
                        case 201:
                        case 202:
                        case 203:
                            ForestAudioManager.Instance.playEffect(AUDIO_CLIP.KEY_BONUS);
                            prizeValue.string = "";
                            prizeValue.node.active = true;
                            this.showMiniGame(price).then(p => {
                                this.left--;
                                this.totalTurn--;
                                prizeValue.string = p.toString();
                            })
                            this.scheduleOnce(()=>{
                                animation.play(eANIMATION.BUTTERFLY);
                                this.checkComplete();
                            }, duration + 0.2);
                            break;
                        case 210:
                            ForestAudioManager.Instance.playEffect(AUDIO_CLIP.MISS_BONUS);
                            this.point++;
                            this.left--;
                            this.scheduleOnce(()=>{
                                animation.play(eANIMATION.YELLOW_FLOWER);
                                this.checkComplete();
                            }, duration + 0.2);
                            break;
                        case 220:
                            ForestAudioManager.Instance.playEffect(AUDIO_CLIP.GOLD_BONUS);
                            prizeValue.string = "0";
                            prizeValue.node.active = true;
                            TweenUtils.numberTo(node["_price"], price, 0.3);
                            this.left--;
                            this.totalTurn--;
                            this.scheduleOnce(()=>{
                                animation.play(eANIMATION.BARREL);
                                this.checkComplete();
                            }, duration + 0.2);
                            break;
                    }
                }
            })
        }
    }

    public showPopup(bonusData: string, startBonus: number) {
        for (let i = 0; i < this.items.children.length; i++) {
            let node = this.items.children[i];
            node.attr({
                _button: node.getComponent(Button),
                _price: node.getChildByName("Price").getComponent(Label),
                _animation: node.getComponent(Animation),
                _isOpen: false,
            });
            (node["_button"] as Button).interactable = true;
            (node["_price"] as Label).node.active = false;
            (node["_animation"] as Animation).play(eANIMATION.WORM)
        }

        this.bonusData = [...bonusData.split(";").map(entry => {
            const values = entry.split(",").map(Number);
            return [values[0], values[1], values[2], values[3]] as [number, number, number, number];
        })];

        this.totalTurn = 10;
        this.startBonus = startBonus;
        this.point = 1;
        this.lblHeso.string = `x${startBonus}`;
        this.quickPlay.active = true;
        this.left = this.bonusData.length;
        // this.win = 0;
        this.resetMiniGame();

        this.startCountdown(15);
    }

    public hidePopup() {
        this.stopCountdown();
        this.lblCountDownTime.string = "";
        this.popout.getChildByName("Popup").children.forEach(item => item.getComponent(Button).interactable = false);
        this.node.active = false;
        if (this.onFinishProcess) {
            const totalWin = this.bonusData.reduce((sum, item) => sum + item[3], 0)
            this.onFinishProcess(totalWin);
        }
    }

    private resetMiniGame() {
        let itemList = this.popout.getChildByName("Popup")
        for (let i = 0; i < itemList.children.length; i++) {
            let item = itemList.children[i];
            let animation = item.getComponent(Animation);
            let prize = item.getComponentInChildren(Label);
            item.getComponent(Button).interactable = true;

            animation.play(eANIMATION.BUTTERFLY);
            prize.node.active = false;  
        }
        this.popout.active = false;
    }

    private async showMiniGame(prizeValue: number): Promise<number> {
        this.popout.active = true;
        let popup = this.popout.getChildByName("Popup")

        popup.getComponent(UIOpacity).opacity = 0;
        tween(popup.getComponent(UIOpacity))
            .to(0.5, { opacity: 255 }, { easing: easing.expoIn })
            .start();

        return new Promise((resolve) => {
            for (let i = 0; i < popup.children.length; i++) {
                let item = popup.children[i];
                let animation = item.getComponent(Animation);
                let prize = item.getComponentInChildren(Label);
                item.on("click", () => {
                    prize.node.active = true;
                    TweenUtils.numberTo(prize, prizeValue, 0.3);
                    animation.play(eANIMATION.BARREL);
                    popup.children.forEach(item => item.getComponent(Button).interactable = false);
                    this.scheduleOnce(() => {
                        this.resetMiniGame();
                        resolve(prizeValue);
                    }, 1.3)
                })
            }
        })
    }

    private startCountdown(seconds: number): void {
        this.time = seconds;
        this.lblCountDownTime.string = (App.instance.getTextLang("sl78")).replace("15", seconds.toString());
        this.countdownInterval = setInterval(() => {
            this.time--;
            this.lblCountDownTime.string = (App.instance.getTextLang("sl78")).replace("15", this.time.toString());
            if (this.time <= 0) {
                clearInterval(this.countdownInterval);
                this.hidePopup();
            }
        }, 1000);
    }

    private checkComplete(){
        // if total turn = 0, then hide
        if (this.totalTurn <= 0) {
            this.scheduleOnce(()=>{
                this.hidePopup();
            },1)
        }
    }

    private stopCountdown(): void {
        if (this.countdownInterval !== null) {
            clearInterval(this.countdownInterval);
            this.countdownInterval = null;
        }
    }
}
