export class ForestTrial{
    static readonly Result = [
        {
            "SpinID": 0,
            "SlotsData": "2,6,4,6,7,3,7,7,7,7,5,5,7,6,3",
            "PrizesData": "1,17,600;5,17,600;12,17,600;16,17,600",
            "PositionData": "7,8,9,10;7,9,10,13;5,7,9,13;5,7,8,9",
            "TotalBetValue": 2000,
            "IsJackpot": false,
            "Jackpot": 500600,
            "JackpotNum": 0,
            "ResponseStatus": 0,
            "IsFreeSpin": false,
            "TotalFreeSpin": 0,
            "StartBonus": 0,
            "BonusGameData": "",
            "PayLinePrizeValue": 2400,
            "TicketBalance": 0,
            "IsLastFreeSpin": false
        },
        {
            "SpinID": 0,
            "SlotsData": "7,6,6,7,5,7,7,7,7,7,7,6,7,7,7",
            "PrizesData": "1,16,3000;3,17,600;4,17,600;5,16,3000;7,17,600;9,17,600;10,17,600;11,17,600;12,17,600;13,17,600;14,17,600;15,16,3000;16,17,600;17,17,600;20,17,600",
            "PositionData": "6,7,8,9,10;11,13,14,15;6,7,9,10;6,7,9,10,13;8,11,14,15;4,11,13,15;4,6,10,13;7,9,11,15;1,7,9,13;4,6,8,10;6,8,10,14;7,8,9,11,15;1,7,8,9;6,10,13,14;1,8,14,15",
            "TotalBetValue": 2000,
            "IsJackpot": false,
            "Jackpot": 500600,
            "JackpotNum": 0,
            "ResponseStatus": 0,
            "IsFreeSpin": false,
            "TotalFreeSpin": 0,
            "StartBonus": 0,
            "BonusGameData": "",
            "PayLinePrizeValue": 16200,
            "TicketBalance": 0,
            "IsLastFreeSpin": false
        },
        {
            "SpinID": 0,
            "SlotsData": "6,2,3,6,6,4,5,7,3,4,5,5,7,7,7",
            "PrizesData": "2,15,200;6,15,200",
            "PositionData": "1,4,5;1,4,5",
            "TotalBetValue": 2000,
            "IsJackpot": false,
            "Jackpot": 500700,
            "JackpotNum": 0,
            "ResponseStatus": 0,
            "IsFreeSpin": false,
            "TotalFreeSpin": 0,
            "StartBonus": 0,
            "BonusGameData": "",
            "PayLinePrizeValue": 400,
            "TicketBalance": 0,
            "IsLastFreeSpin": false
        },
        {
            "SpinID": 0,
            "SlotsData": "2,6,7,4,7,6,7,5,4,5,3,2,5,3,1",
            "PrizesData": "",
            "PositionData": "",
            "TotalBetValue": 0,
            "IsJackpot": false,
            "Jackpot": 500700,
            "JackpotNum": 0,
            "ResponseStatus": 0,
            "IsFreeSpin": false,
            "TotalFreeSpin": 0,
            "StartBonus": 0,
            "BonusGameData": "",
            "PayLinePrizeValue": 0,
            "TicketBalance": 0,
            "IsLastFreeSpin": false
        },
        {
            "SpinID": 0,
            "SlotsData": "1,5,3,5,5,2,7,7,4,7,6,7,7,4,6",
            "PrizesData": "2,12,300;6,12,300",
            "PositionData": "2,4,5;2,4,5",
            "TotalBetValue": 2000,
            "IsJackpot": false,
            "Jackpot": 500800,
            "JackpotNum": 0,
            "ResponseStatus": 0,
            "IsFreeSpin": false,
            "TotalFreeSpin": 0,
            "StartBonus": 0,
            "BonusGameData": "",
            "PayLinePrizeValue": 600,
            "TicketBalance": 0,
            "IsLastFreeSpin": false
        },
        {
            "SpinID": 0,
            "SlotsData": "7,3,2,7,2,7,6,7,4,4,7,7,7,3,6",
            "PrizesData": "13,17,600;19,17,600",
            "PositionData": "4,6,8,12;4,8,11,12",
            "TotalBetValue": 2000,
            "IsJackpot": false,
            "Jackpot": 500800,
            "JackpotNum": 0,
            "ResponseStatus": 0,
            "IsFreeSpin": false,
            "TotalFreeSpin": 0,
            "StartBonus": 0,
            "BonusGameData": "",
            "PayLinePrizeValue": 1200,
            "TicketBalance": 0,
            "IsLastFreeSpin": false
        },
        {
            "SpinID": 0,
            "SlotsData": "5,6,5,7,4,7,4,7,7,6,6,5,2,3,5",
            "PrizesData": "8,12,300",
            "PositionData": "1,3,12",
            "TotalBetValue": 2000,
            "IsJackpot": false,
            "Jackpot": 500900,
            "JackpotNum": 0,
            "ResponseStatus": 0,
            "IsFreeSpin": false,
            "TotalFreeSpin": 0,
            "StartBonus": 0,
            "BonusGameData": "",
            "PayLinePrizeValue": 300,
            "TicketBalance": 0,
            "IsLastFreeSpin": false
        },
        {
            "SpinID": 0,
            "SlotsData": "7,3,3,2,3,4,1,5,3,7,7,4,1,4,7",
            "PrizesData": "2,6,500;17,9,400",
            "PositionData": "2,3,5;6,12,14",
            "TotalBetValue": 2000,
            "IsJackpot": false,
            "Jackpot": 500900,
            "JackpotNum": 0,
            "ResponseStatus": 0,
            "IsFreeSpin": false,
            "TotalFreeSpin": 0,
            "StartBonus": 0,
            "BonusGameData": "",
            "PayLinePrizeValue": 900,
            "TicketBalance": 0,
            "IsLastFreeSpin": false
        },
        {
            "SpinID": 0,
            "SlotsData": "6,6,6,3,4,7,6,4,4,3,5,3,6,4,7",
            "PrizesData": "2,15,200;12,15,200;13,6,500;16,9,400",
            "PositionData": "1,2,3;1,7,13;4,10,12;5,8,9",
            "TotalBetValue": 2000,
            "IsJackpot": false,
            "Jackpot": 500900,
            "JackpotNum": 0,
            "ResponseStatus": 0,
            "IsFreeSpin": false,
            "TotalFreeSpin": 0,
            "StartBonus": 0,
            "BonusGameData": "",
            "PayLinePrizeValue": 1300,
            "TicketBalance": 0,
            "IsLastFreeSpin": false
        },
        {
            "SpinID": 0,
            "SlotsData": "4,7,7,1,7,4,3,3,5,5,1,7,4,5,6",
            "PrizesData": "",
            "PositionData": "",
            "TotalBetValue": 2000,
            "IsJackpot": false,
            "Jackpot": 500900,
            "JackpotNum": 0,
            "ResponseStatus": 0,
            "IsFreeSpin": false,
            "TotalFreeSpin": 0,
            "StartBonus": 0,
            "BonusGameData": "",
            "PayLinePrizeValue": 0,
            "TicketBalance": 0,
            "IsLastFreeSpin": false
        }
    ]
}
