import { BaseSlotMachine } from "db://assets/Lobby/scripts/common/slot/BaseSlotMachine";
import AudioManager, { AUDIO_CLIP } from "../AudioManager";
import { _decorator, Node } from 'cc';
import OceanSlotSymbol from "./OceanSlotSymbol";
import { Utils } from "db://assets/Lobby/scripts/common/Utils";

const {ccclass, property} = _decorator;

const TURBO_CONFIG = {
    elasticPercent: 15,
    symbolOffset: 30,
    spinDuration: 1.5,
    delayReel: 0.1,
    rowCount: 3,
};

const NORMAL_CONFIG = {
    elasticPercent: 20,
    symbolOffset: 30,
    spinDuration: 3,
    delayReel: 0.3,
    rowCount: 3,
};

@ccclass
export default class OceanSlotMachine extends BaseSlotMachine {

    protected onLoad(): void {
        this.listeners = {
            onReelCompleted: () => {
                AudioManager.Instance.playEffect(AUDIO_CLIP.END_REEL_SPIN);
            }
        }
    }

    protected initializeSymbol(symbolNode: Node, isBlur: boolean): void {
        let sID = `${Utils.randomRangeInt(1, 8)}`
        symbolNode.getComponent(OceanSlotSymbol).setIsBlur(isBlur).setId(sID).show();
    }

    override async highlightItems(indexes: number[]): Promise<void> {
        await super.highlightItems(indexes);
    }

    override async resetAllItems(): Promise<void> {
        super.resetAllItems();
    }

    public setTurbo(isTurbo: boolean){
        this.setConfig(isTurbo ? TURBO_CONFIG : NORMAL_CONFIG);
    }
}
