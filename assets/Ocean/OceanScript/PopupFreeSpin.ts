import Dialog from "../../Lobby/LobbyScript/Script/common/Dialog";
import Tween from "../../Lobby/LobbyScript/Script/common/Tween";
import Utils from "../../Lobby/LobbyScript/Script/common/Utils";

const {ccclass, property, menu} = cc._decorator;

@ccclass
@menu("Ocean/PopupFreeSpin")
export class PopupFreeSpin extends Dialog {
    @property(cc.Label)
    lblReward: cc.Label = null;

    private _resolve: () => void = null;

    public showPopup(reward: number) : Promise<void>{
        return new Promise<void>((resolve) => {
            this._resolve = resolve;
            this.lblReward.string = '';
            Tween.numberTo(this.lblReward, reward, 0.3);
            super.show();
        });
    }

    public dismiss() {
        super.dismiss();
    }

    _onShowed() {
        super._onShowed();
        this.scheduleOnce(() => {
            this.dismiss();
        }, 3);
    }

    _onDismissed() {
        super._onDismissed();
        if (this._resolve) {
            this._resolve();
            this._resolve = null;
        }
    }
}
