import App from "../../Lobby/LobbyScript/Script/common/App";
import Tween from "../../Lobby/LobbyScript/Script/common/Tween";
import Utils from "../../Lobby/LobbyScript/Script/common/Utils";
import AudioManager, { AUDIO_CLIP } from "./AudioManager";

const { ccclass, property, menu } = cc._decorator;

@ccclass
@menu("Ocean/OceanBonus")
export class OceanBonus extends cc.Component {
    @property(cc.Node) items: cc.Node = null;
    @property(cc.Node) miniGameItems: cc.Node = null;

    @property(cc.Label) lblTurn: cc.Label = null;
    @property(cc.Label) lblPoint: cc.Label = null;
    @property(cc.Label) lblCountDownTime: cc.Label = null;

    @property(cc.Node) miniGame: cc.Node = null;
    @property(cc.Node) btnQuickPlay: cc.Node = null;

    @property(cc.Node) completeScene: cc.Node = null;

    @property(cc.SpriteFrame) box: cc.SpriteFrame = null;
    @property(cc.SpriteFrame) boxGold: cc.SpriteFrame = null;
    @property(cc.SpriteFrame) boxMiniGame: cc.SpriteFrame = null;
    @property(cc.SpriteFrame) boxPoint: cc.SpriteFrame = null;

    @property(cc.SpriteFrame) miniGameBox: cc.SpriteFrame = null;
    @property(cc.SpriteFrame) miniGameBoxGold: cc.SpriteFrame = null;

    private _countdownInterval: number;
    bonusData: Array<[number, number, number, number]> = [];
    onFinished: Function = null;
    totalTurn: number;
    startBonus: number;
    accumulate = 0;
    left = 0;
    time = 0;
    lockInput = false;
    lockInputMiniGame = false;
    protected onEnable(): void {
        for (let i = 0; i < this.items.childrenCount; i++) {
            let node = this.items.children[i];
            node["btn"] = node.getComponent(cc.Button);
            node["label"] = node.getChildByName("label").getComponent(cc.Label);
            node["sprite"] = node.getChildByName("sprite").getComponent(cc.Sprite);

            node.off("click");
            node.once("click", () => {
                if (this.lockInput) return;
                AudioManager.Instance.playEffect(AUDIO_CLIP.CLICK);
                this.btnQuickPlay.active = false;
                this._clearTimer();
                const [step, rewardID, multiplier, price] = this.bonusData[this.bonusData.length - this.left];
                switch (rewardID) {
                    case 201:
                    case 202:
                    case 203:
                        this.lockInput = true;
                        this.scheduleOnce(() => {
                            this.showMiniGame(price, () => {
                                this.lockInput = false;
                                this.left--;
                                this.totalTurn--;
                                this.updateGame();
                                node['label'].node.active = true;
                                node['label'].string = Utils.formatNumber(price);
                            })
                        }, 0.5)
                        node["sprite"].spriteFrame = this.boxMiniGame;
                        break;
                    case 210:
                        this.left--;
                        this.accumulate++;
                        this.updateGame();
                        node["sprite"].spriteFrame = this.boxPoint;
                        break;
                    case 220:
                        node['label'].node.active = true;
                        node['label'].string = "0";
                        Tween.numberTo(node['label'], price, 0.5);
                        this.left--;
                        this.totalTurn--;
                        this.updateGame();
                        node["sprite"].spriteFrame = this.boxGold;
                        break;
                }
            })
        }
    }

    showBonus(data, startBonus: number) {

        this.node.active = true;
        this.items.active = true;
        this.miniGame.active = false;
        this.completeScene.active = false;

        for (let i = 0; i < this.items.childrenCount; i++) {
            let node = this.items.children[i];

            node["btn"] = node.getComponent(cc.Button);
            node["label"] = node.getChildByName("label").getComponent(cc.Label);
            node["sprite"] = node.getChildByName("sprite").getComponent(cc.Sprite);

            node["btn"].interactable = true;
            node["label"].node.active = false;
            node["sprite"].spriteFrame = this.box;

        }

        this.bonusData = [];
        for (let d of data.split(";")) {
            this.bonusData.push(d.split(",").map(Number));
        }
        this.totalTurn = 10;
        this.lockInput = false;
        this.accumulate = 1;
        this.startBonus = startBonus;
        this.btnQuickPlay.active = true;
        this.lblPoint.node.parent.active = true;
        this.lblTurn.node.parent.active = true;
        this.left = this.bonusData.length;

        this.startCountdown(15);
        this.updateGame();
    }

    showMiniGame(prize: number, cb: Function) {
        this.miniGame.active = true;
        this.lockInputMiniGame = false;

        for (let i = 0; i < this.miniGameItems.childrenCount; i++) {
            let node = this.miniGameItems.children[i];

            node["btn"] = node.getComponent(cc.Button);
            node["label"] = node.getChildByName("label").getComponent(cc.Label);
            node["sprite"] = node.getChildByName("sprite").getComponent(cc.Sprite);

            node["btn"].interactable = true;
            node["label"].node.active = false;
            node["sprite"].spriteFrame = this.miniGameBox;
        }

        for (let i = 0; i < this.miniGameItems.childrenCount; i++) {
            let node = this.miniGameItems.children[i];
            node.off('click');
            node.once('click', () => {
                if (this.lockInputMiniGame) return;
                this.lockInputMiniGame = true;
                AudioManager.Instance.playEffect(AUDIO_CLIP.CLICK);
                node["btn"].interactable = false;
                node["label"].node.active = true;
                node["label"].string = "0";
                Tween.numberTo(node["label"], prize, 0.5);
                node["sprite"].spriteFrame = this.miniGameBoxGold;

                this.scheduleOnce(() => {
                    this.miniGame.active = false;
                    cb && cb();
                }, 1)
            });
        }
    }

    private startCountdown(seconds: number): void {
        this.time = seconds;
        this.lblCountDownTime.string = this.getTimeString(seconds);
        this._countdownInterval = setInterval(() => {
            this.time--;
            this.lblCountDownTime.string = this.getTimeString(this.time);
            if (this.time <= 0) {
                clearInterval(this._countdownInterval);
                this.totalTurn = 0;
                this.updateGame();
            }
        }, 1000);
    }

    private _clearTimer() {
        if (this._countdownInterval !== null) {
            clearInterval(this._countdownInterval);
            this._countdownInterval = null;
        }
    }

    private getTimeString(remainTime: number): string {
        let text = App.instance.getTextLang("sl78");
        return text.replace("15", remainTime.toString());
    }

    private updateGame() {
        this.lblTurn.string = this.totalTurn.toString();
        this.lblPoint.string = this.accumulate.toString();

        if (this.totalTurn <= 0) {
            this.lockInput = true;
            this._clearTimer();
            this.scheduleOnce(() => {
                this.showCompleteBonusGame();
            }, 1)
            this.scheduleOnce(() => {
                this.node.active = false;
                this.onFinished && this.onFinished();
            }, 3)
        }
    }

    private showCompleteBonusGame() {
        AudioManager.Instance.playEffect(AUDIO_CLIP.REWARD_BONUS);
        const finalWinPrize = this.bonusData.reduce((sum, item) => sum + item[3], 0);
        this.lblPoint.node.parent.active = false;
        this.lblTurn.node.parent.active = false;
        this.completeScene.active = true;
        this.miniGame.active = false;
        this.items.active = false;

        let label = this.completeScene.getChildByName("WinBonus");
        label.getComponent(cc.Label).string = "0";
        Tween.numberTo(label.getComponent(cc.Label), finalWinPrize, 0.5);
    }

    private onClickQuickPlay() {
        AudioManager.Instance.playEffect(AUDIO_CLIP.CLICK);
        this.btnQuickPlay.active = false;
        this._clearTimer();
        this.startCountdown(1);
    }

}
