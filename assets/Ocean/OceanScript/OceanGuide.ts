import { OceanController } from "./OceanController";

const {ccclass, property, menu} = cc._decorator;

@ccclass
@menu("Ocean/OceanGuide")
export class OceanGuide extends cc.Component {
    @property(cc.Node)
    private paytable1: cc.Node = null;

    @property(cc.Node)
    private paytable2: cc.Node = null;

    @property(cc.Node)
    private paytable3: cc.Node = null;


    onLoad(){
        OceanController.getInstance().addObserver(this.node.uuid, {
            onChangeRoom: (sender: OceanController, roomID: number) => {
                this.onRoomChange(roomID);
            }
        });
    }

    onRoomChange(roomID: number): void {
        this.paytable1.active = roomID == 1;
        this.paytable2.active = roomID == 2;
        this.paytable3.active = roomID == 3;
    }

    protected onDestroy(): void {
        OceanController.getInstance().removeObserver(this.node.uuid);
    }

    //show and dismiss
    show() {
        this.node.active = true;
    }

    dismiss() {
        this.node.active = false;
    }
}
