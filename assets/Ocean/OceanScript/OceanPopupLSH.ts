import App from "../../Lobby/LobbyScript/Script/common/App";
import Utils from "../../Lobby/LobbyScript/Script/common/Utils";
import Configs from "../../Lobby/MoveScript/Configs";
import Http from "../../Lobby/MoveScript/Http";

const { ccclass, property, menu } = cc._decorator;

@ccclass
@menu("Ocean/OceanPopupLSH")
export class OceanPopupLSH extends cc.Component {
    @property(cc.Node)
    item: cc.Node = null;

    @property(cc.Node)
    itemContainer: cc.Node = null;

    private currentPage: number = 1;
    private pageSize: number = 15;
    private isLoading: boolean = false;
    private hasMoreData: boolean = true;

    show() {
        this.node.active = true;
        this.itemContainer.removeAllChildren();
        this.resetPagination();
        this.loadData().then();
        this.getComponentInChildren(cc.ScrollView).node.on('scroll-to-bottom', this.onScrollToBottom, this);
    }

    dismiss() {
        this.node.active = false;
        this.getComponentInChildren(cc.ScrollView).node.off('scroll-to-bottom', this.onScrollToBottom, this);
    }

    private resetPagination() {
        this.currentPage = 1;
        this.hasMoreData = true;
        this.isLoading = false;
    }

    private onScrollToBottom() {
        if (!this.isLoading && this.hasMoreData) {
            this.loadData().then();
        }
    }

    private async loadData() {
        this.isLoading = true;
        App.instance.showLoading(true);
        try {
            const result = await this.fetchHistory(this.currentPage);
            this.appendData(result);
            this.hasMoreData = result.length >= this.pageSize;
        } catch (e) {
            cc.error("Error fetching Jackpot History", e);
        } finally {
            this.isLoading = false;
            App.instance.showLoading(false);
        }
    }

    private fetchHistory(page: number): Promise<any[]> {
        return new Promise((resolve, reject) => {
            Http.get(Configs.App.DOMAIN_CONFIG['Ocean_GetTransactionLog'], {
                "CurrencyID": Configs.Login.CurrencyID,
                "type": 2,
                "Page": page,
                "PageSize": this.pageSize,
            }, (status, response) => {
                if (status === 200) {
                    resolve(response["d"]);
                } else {
                    reject(new Error("Error fetching Jackpot History"));
                }
            })
        })
    }

    private appendData(result: any[]) {
        this.createItems(result);
    }

    private createItems(result: any[]) {
        for (let i = 0; i < result.length; i++) {
            const item = cc.instantiate(this.item);
            item.active = true;
            this.itemContainer.addChild(item);
            item.children[0].getComponent(cc.Label).string = Utils.formatDatetime(result[i].createTime, "dd/MM/yyyy HH:mm:ss");

            // room 1 is 100, room 2 is 1000, room 3 is 10000
            item.children[1].getComponent(cc.Label).string = result[i].betValue;
            item.children[2].getComponent(cc.Label).string = `${result[i].nickname}`;
            item.children[3].getComponent(cc.Label).string = `${result[i].jackPotNum}`;
            item.children[4].getComponent(cc.Label).string = Utils.formatNumber(result[i].paylinePrizeValue);
        }
    }
}
