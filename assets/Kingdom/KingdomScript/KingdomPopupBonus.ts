import { _decorator, Component, Node, Label, Button, Animation, tween, Tween } from "cc";
import { AUDIO_CLIP, KingdomAudioManager } from "./KingdomAudioManager";
import App from "../../Lobby/scripts/common/App";
import { TweenUtils } from "../../Lobby/scripts/common/TweenUtils";

const { ccclass, property, menu } = _decorator;


enum eANIMATION {
    FIRE = 'fire',
    SKULL = 'skull',
    DIAMOND = 'diamond',
    MEDAL = 'medal',
}

@ccclass
@menu("Kingdom/PopupBonus")
export default class KingdomPopupBonus extends Component {
    @property(Node) items: Node = null;
    @property(Node) items2: Node = null;

    @property(Label) lblTurn: Label = null;
    @property(Label) lblPoint: Label = null;
    @property(Label) lblCountDownTime: Label = null;

    @property(Node) miniGame: Node = null;
    @property(Node) quickPlay: Node = null;

    private _countdownInterval: number;

    bonusData: Array<[number, number, number, number]> = [];
    onFinished: Function = null;
    totalTurn: number;
    startBonus: number;
    amulate = 0;
    left = 0;
    time = 0;


    protected onLoad() {
        for (let i = 0; i < this.items.children.length; i++) {
            let node = this.items.children[i];
            node["btn"] = node.getComponent(Button);
            node["label"] = node.getChildByName("Price").getComponent(Label);
            node["anim"] = node.getComponent(Animation);
            node["isOpen"] = false;

            node.on("click", () => {
                this.quickPlay.active = false;

                if (this._countdownInterval !== null) {
                    clearInterval(this._countdownInterval);
                    this._countdownInterval = null;
                }

                if (node["isOpen"] === false) {
                    const [step, rewardID, multiplier, price] = this.bonusData[this.bonusData.length - this.left];
                    node["isOpen"] = true;


                    switch (rewardID) {
                        case 201:
                        case 202:
                        case 203:
                            KingdomAudioManager.Instance.playEffect(AUDIO_CLIP.BONUS_BONUS);
                            node["label"].string = "0";
                            node["label"].node.active = true;
                            // use tween utils
                            TweenUtils.numberTo(node["label"], price, 1);
                            // Tween.numberTo(node["label"], price, 1);
                            this.showMiniGame(price, () => {
                                this.left--;
                                this.totalTurn--;
                                this._updateUI();
                            })
                            node["anim"].play(eANIMATION.DIAMOND);
                            break;
                        case 210:
                            KingdomAudioManager.Instance.playEffect(AUDIO_CLIP.BONUS_LOSE);
                            node["anim"].play(eANIMATION.SKULL);
                            this.left--;
                            this.amulate++;
                            this._updateUI();
                            break;
                        case 220:
                            KingdomAudioManager.Instance.playEffect(AUDIO_CLIP.CLICK);
                            node["label"].string = "0";
                            node["label"].node.active = true;
                            TweenUtils.numberTo(node["label"], price, 0.3);
                            node["anim"].play(eANIMATION.MEDAL);
                            this.left--;
                            this.totalTurn--;
                            this._updateUI();
                            break;
                    }
                }
            })
        }

        for (let i = 0; i < this.items2.children.length; i++) {
            let node = this.items2.children[i];
            node["btn"] = node.getComponent(Button);
            node["label"] = node.getChildByName("Price").getComponent(Label);
            node["anim"] = node.getComponent(Animation);
            node["isOpen"] = false;
        }
    }



    public showPopup(data, startBonus: number) {
        this.node.active = true;
        this.miniGame.active = false;

        for (let i = 0; i < this.items.children.length; i++) {
            let node = this.items.children[i];
            node["isOpen"] = false;
            node["btn"].interactable = true;
            node["label"].node.active = false;
            node["anim"].play(eANIMATION.FIRE)
        }

        this.bonusData = [];
        for (let d of data.split(";")) {
            this.bonusData.push(d.split(",").map(Number));
        }

        this.totalTurn = 10;
        this.amulate = 1;
        this.startBonus = startBonus;
        this.quickPlay.active = true;
        this.lblTurn.node.parent.active = true;
        this.lblPoint.node.parent.active = true;
        this.left = this.bonusData.length;

        this.startCountdown(15);
        this._updateUI();
    }

    public completeGame() {
        clearInterval(this._countdownInterval);
        this.node.active = false;
        this.onFinished && this.onFinished();
    }

    private showMiniGame(prizeValue: number, cb: () => void) {
        this.miniGame.active = true;

        for (let i = 0; i < this.items2.children.length; i++) {
            let node = this.items2.children[i];
            node["btn"] = node.getComponent(Button);
            node["label"] = node.getChildByName("Price").getComponent(Label);
            node["anim"] = node.getComponent(Animation);

            node["btn"].interactable = true;
            node["label"].node.active = false;
            node["anim"].play(eANIMATION.DIAMOND);

            node["label"].string = "";
            node["isOpen"] = false;
            node.off("click");
            node.on("click", () => {
                if (node["isOpen"] === false) {
                    KingdomAudioManager.Instance.playEffect(AUDIO_CLIP.CLICK);
                    node["isOpen"] = true;
                    node["btn"].interactable = false;
                    node["label"].node.active = true;
                    TweenUtils.numberTo(node["label"], prizeValue, 0.5);
                    node["anim"].play(eANIMATION.MEDAL);

                    this.scheduleOnce(() => {
                        this.miniGame.active = false;
                        cb && cb();
                    }, 1)
                }
            })
        }
    }

    private startCountdown(seconds: number): void {
        this.time = seconds;
        this.lblCountDownTime.string = this.getTimeString(seconds);
        this._countdownInterval = setInterval(() => {
            this.time--;
            this.lblCountDownTime.string = this.getTimeString(this.time);
            if (this.time <= 0) {
                clearInterval(this._countdownInterval);
                this.node.active = false;
                this.onFinished && this.onFinished();
            }
        }, 1000);
    }

    getTimeString(remainTime: number): string {
        let text = App.instance.getTextLang("sl78");
        return text.replace("15", remainTime.toString());
    }

    private _updateUI() {
        this.lblTurn.string = this.totalTurn.toString();
        this.lblPoint.string = this.amulate.toString();

        // if turn = 0, then hide
        if (this.totalTurn <= 0) {
            clearInterval(this._countdownInterval);
            this.scheduleOnce(() => {
                this.node.active = false;
                this.onFinished && this.onFinished();
            }, 1)
        }
    }

    getFinishWin() {
        return this.bonusData.reduce((sum, item) => sum + item[3], 0)
    }

}
