import Dialog from "../../Lobby/LobbyScript/Script/common/Dialog";
import App from "../../Lobby/LobbyScript/Script/common/App";
import Http from "../../Lobby/MoveScript/Http";
import Configs from "../../Lobby/MoveScript/Configs";
import Utils from "../../Lobby/LobbyScript/Script/common/Utils";

const { ccclass, property, menu } = cc._decorator;

@ccclass
@menu("Kingdom/PopupLSC")
export default class KingdomPopupLSC extends cc.Component {
    @property(cc.Node)
    itemTemplate: cc.Node = null;

    @property(cc.Node)
    itemContainer: cc.Node = null;

    private currentPage: number = 1;
    private readonly pageSize: number = 11;
    private isLoading: boolean = false;
    private hasMoreData: boolean = true;

    show() {
        this.node.active = true;
        this.itemContainer.removeAllChildren();
        this.resetPagination();
        this.loadData().then();

        this.getComponentInChildren(cc.ScrollView).node.on('scroll-to-bottom', this.onScrollToBottom, this);
    }

    dismiss() {
        this.node.active = false;
        this.getComponentInChildren(cc.ScrollView).node.off('scroll-to-bottom', this.onScrollToBottom, this);
    }

    private resetPagination() {
        this.currentPage = 1;
        this.hasMoreData = true;
        this.isLoading = false;
    }

    private onScrollToBottom() {
        if (!this.isLoading && this.hasMoreData) {
            this.loadMoreData().then();
        }
    }

    private async loadData() {
        this.isLoading = true;
        App.instance.showLoading(true);
        try {
            const result = await this.fetchHistory(this.currentPage);
            this.displayData(result);
            // Check if we have more data
            this.hasMoreData = result.length >= this.pageSize;
        } catch (e) {
            cc.error("Error fetching History", e);
        } finally {
            this.isLoading = false;
            App.instance.showLoading(false);
        }
    }

    private async loadMoreData() {
        if (this.isLoading || !this.hasMoreData) return;

        this.isLoading = true;
        App.instance.showLoading(true);
        try {
            this.currentPage++;
            const result = await this.fetchHistory(this.currentPage);
            this.appendData(result);
            // Check if we have more data
            this.hasMoreData = result.length >= this.pageSize;
        } catch (e) {
            this.currentPage--; // Rollback page increment if error occurs
            cc.error("Error fetching more History", e);
        } finally {
            this.isLoading = false;
            App.instance.showLoading(false);
        }
    }

    private fetchHistory(page: number): Promise<any[]> {
        return new Promise((resolve, reject) => {
            Http.get(Configs.App.DOMAIN_CONFIG['Kingdom_GetTransactionLog'], {
                "CurrencyID": Configs.Login.CurrencyID,
                "type": 1,
                "Page": page,
                "PageSize": this.pageSize,
            }, (status, response) => {
                if (status === 200) {
                    resolve(response["d"]);
                } else {
                    reject(new Error("Error fetching History"));
                }
            })
        })
    }

    private displayData(result: any[]) {
        this.itemContainer.removeAllChildren();
        this.createItems(result);
    }

    private appendData(result: any[]) {
        this.createItems(result);
    }

    private createItems(result: any[]) {
        for (let i = 0; i < result.length; i++) {
            let itemRow = cc.instantiate(this.itemTemplate);
            itemRow.active = true;
            this.itemContainer.addChild(itemRow);

            let winLineLength = result[i].prizeData ? result[i].prizeData.split(";").length : 0;
            // Populate the row with data
            itemRow.children[0].getComponent(cc.Label).string = result[i].spinID;
            itemRow.children[1].getComponent(cc.Label).string = Utils.formatDatetime(result[i].createTime, "dd/MM/yyyy HH:mm:ss");
            itemRow.children[2].getComponent(cc.Label).string = Utils.formatNumberMin(result[i].betValue);
            itemRow.children[3].getComponent(cc.Label).string = Utils.formatNumber(result[i].totalBetValue)
            itemRow.children[4].getComponent(cc.Label).string = winLineLength.toString() + " " + App.instance.getTextLang('oc7');
            itemRow.children[5].getComponent(cc.Label).string = Utils.formatNumber(result[i].paylinePrizeValue);
        }
    }
}