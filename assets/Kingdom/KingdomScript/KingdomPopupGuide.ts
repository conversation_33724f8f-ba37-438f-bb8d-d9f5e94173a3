const {ccclass, property, menu  } = cc._decorator;

@ccclass
@menu("Kingdom/PopupGuide")
export default class KingdomPopupGuide extends cc.Component {
    @property([cc.SpriteFrame])
    private frames: cc.SpriteFrame[] = [];

    @property(cc.Sprite)
    private content: cc.Sprite = null;

    // onLoad() {
    //     cc.systemEvent.on("ChangeRoom", this.onRoomChange, this);
    // }

    // onDestroy() {
    //     cc.systemEvent.off("ChangeRoom", this.onRoomChange, this);
    // }

    public onRoomChange(roomID: number): void {
        this.content.spriteFrame = this.frames[roomID - 1];
    }

    show() {
        this.node.active = true;
    }

    dismiss() {
        this.node.active = false;
    }
}