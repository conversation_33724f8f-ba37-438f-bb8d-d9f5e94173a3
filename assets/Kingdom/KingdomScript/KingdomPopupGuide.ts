import { _decorator, Component, SpriteFrame, Sprite } from 'cc';
const {ccclass, property, menu  } = _decorator;

@ccclass
@menu("Kingdom/PopupGuide")
export default class KingdomPopupGuide extends Component {
    @property([SpriteFrame])
    private frames: SpriteFrame[] = [];

    @property(Sprite)
    private content: Sprite = null;

    public onRoomChange(roomID: number): void {
        this.content.spriteFrame = this.frames[roomID - 1];
    }

    show() {
        this.node.active = true;
    }

    dismiss() {
        this.node.active = false;
    }
}