import { _decorator, Component, Node, Sprite<PERSON><PERSON>e, Button, Sprite, Toggle } from "cc";
import { AUDIO_CLIP, KingdomAudioManager } from "./KingdomAudioManager";

const { ccclass, property, menu } = _decorator;

@ccclass
@menu("Kingdom/PopupSelectLine")
export default class KingdomPopupSelectLine extends Component {
    @property(Node)
    private buttonLines: Node = null;

    @property(SpriteFrame)
    private pressedSpriteFrame: SpriteFrame = null;

    @property(SpriteFrame)
    private normalSpriteFrame: SpriteFrame = null;

    @property(Button)
    private btnBetRoom1: Button = null;

    @property(Button)
    private btnBetRoom2: Button = null;

    @property(Button)
    private btnBetRoom3: Button = null;

    private _currentRoom: number = 1;
    public get currentRoom(): number {
        return this._currentRoom;
    }
    public set currentRoom(value: number) {
        this._currentRoom = value;
        this.updateButtonRoom();
    }

    private updateButtonRoom() {
        this.btnBetRoom1.target.getComponent(Sprite).spriteFrame = this._currentRoom === 1 ? this.pressedSpriteFrame : this.normalSpriteFrame;
        this.btnBetRoom2.target.getComponent(Sprite).spriteFrame = this._currentRoom === 2 ? this.pressedSpriteFrame : this.normalSpriteFrame;
        this.btnBetRoom3.target.getComponent(Sprite).spriteFrame = this._currentRoom === 3 ? this.pressedSpriteFrame : this.normalSpriteFrame;
    }

    private _onSelectedChanged: (selectedLines: number[]) => void = null;

    public setOnSelectedChanged(fn: (selectedLines: number[]) => void) {
        this._onSelectedChanged = fn;
    }

    protected onLoad() {
        this.buttonLines.children.forEach((btn, index) => {
            btn.on("toggle", () => {
                KingdomAudioManager.Instance.playEffect(AUDIO_CLIP.CLICK);
                this.updateSelectedLines();
            });
        });
        this.updateSelectedLines();
    }

    private updateSelectedLines() {
        const selectedLines = this.getSelectedLines();
        this._onSelectedChanged?.(selectedLines);
    }

    public getSelectedLines(): number[] {
        const selectedLines: number[] = [];
        this.buttonLines.children.forEach((btn, index) => {
            if (btn.getComponent(Toggle).isChecked) {
                selectedLines.push(index + 1);
            }
        });
        return selectedLines;
    }

    private toggleLines(condition: (index: number) => boolean) {
        this.buttonLines.children.forEach((btn, index) => {
            const lineNumber = index + 1;
            btn.getComponent(Toggle).isChecked = condition(lineNumber);
        });
        this.updateSelectedLines();
    }

    private activeEvenLines() {
        this.toggleLines(index => index % 2 === 0);
    }

    private activeOddLines() {
        this.toggleLines(index => index % 2 !== 0);
    }

    private activeAllLines() {
        this.toggleLines(() => true);
    }

    private deactiveAllLines() {
        this.toggleLines(() => false);
    }

    show() {
        this.node.active = true;
    }

    dismiss() {
        this.node.active = false;
    }
}