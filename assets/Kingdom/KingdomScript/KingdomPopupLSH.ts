import { _decorator, Component, Node, Label, ScrollView, instantiate, error } from 'cc';
import App from '../../Lobby/scripts/common/App';
import Configs from '../../Lobby/scripts/common/Config';
import Http from '../../Lobby/scripts/common/Http';
import { Utils } from '../../Lobby/scripts/common/Utils';


const {ccclass, property, menu} = _decorator;

@ccclass
@menu("Kingdom/PopupLSH")
export default class KingdomPopupLSH extends Component {
    @property(Node)
    itemTemplate: Node = null;

    @property(Node)
    itemContainer: Node = null;

    private readonly ROOM_BET_VALUES: { [key: number]: number } = {
        1: 100,
        2: 1000,
        3: 10000
    };

    private currentPage: number = 1;
    private readonly pageSize: number = 11;
    private isLoading: boolean = false;
    private hasMoreData: boolean = true;

    show() {
        this.node.active = true;
        this.itemContainer.removeAllChildren();
        this.resetPagination();
        this.loadData().then();

        // Add scroll event listener
        this.getComponentInChildren(ScrollView).node.on('scroll-to-bottom', this.onScrollToBottom, this);
    }

    dismiss() {
        this.node.active = false;
        // Remove scroll event listener
        this.getComponentInChildren(ScrollView).node.off('scroll-to-bottom', this.onScrollToBottom, this);
    }

    private resetPagination() {
        this.currentPage = 1;
        this.hasMoreData = true;
        this.isLoading = false;
    }

    private onScrollToBottom() {
        if (!this.isLoading && this.hasMoreData) {
            this.loadMoreData().then();
        }
    }

    private async loadData() {
        this.isLoading = true;
        App.instance.showLoading(true);
        try {
            const result = await this.fetchHistory(this.currentPage);
            this.displayData(result);
            this.hasMoreData = result.length >= this.pageSize;
        } catch (e) {
            error("Error fetching Jackpot History", e);
        } finally {
            this.isLoading = false;
            App.instance.showLoading(false);
        }
    }

    private async loadMoreData() {
        if (this.isLoading || !this.hasMoreData) return;
        
        this.isLoading = true;
        App.instance.showLoading(true);
        try {
            this.currentPage++;
            const result = await this.fetchHistory(this.currentPage);
            this.appendData(result);
            this.hasMoreData = result.length >= this.pageSize;
        } catch (e) {
            this.currentPage--; // Rollback page increment if error ors
            error("Error fetching more Jackpot History", e);
        } finally {
            this.isLoading = false;
            App.instance.showLoading(false);
        }
    }

    private fetchHistory(page: number): Promise<any[]> {
        return new Promise((resolve, reject) => {
            Http.get(Configs.App.DOMAIN_CONFIG['Kingdom_GetTransactionLog'], {
                "CurrencyID": Configs.Login.CurrencyID,
                "type": 2, // TYPE 2 for jackpot history. TYPE 1 for my history
                "Page": page,
                "PageSize": this.pageSize,
            }, (status, response) => {
                if (status === 200) {
                    resolve(response["d"]);
                } else {
                    reject(new Error("Error fetching Jackpot History"));
                }
            })
        })
    }

    private displayData(result: any[]) {
        this.itemContainer.removeAllChildren();
        this.createItems(result);
    }

    private appendData(result: any[]) {
        this.createItems(result);
    }

    private createItems(result: any[]) {
        for (let i = 0; i < result.length; i++) {
            let itemRow = instantiate(this.itemTemplate);
            itemRow.active = true;
            this.itemContainer.addChild(itemRow);
            
            // Get bet value from room bet values map, default to 100 if not found
            const betValue = this.ROOM_BET_VALUES[result[i].roomID] || 100;
            
            // Populate the row with data based on mockup columns
            itemRow.children[0].getComponent(Label).string = Utils.formatDatetime(result[i].createTime, "dd/MM/yyyy HH:mm:ss");
            itemRow.children[1].getComponent(Label).string = Utils.formatMoneyOnlyK(result[i].betValue);
            // Format player name with [PORTAL] prefix
            itemRow.children[2].getComponent(Label).string = `${result[i].nickname}`;
            itemRow.children[3].getComponent(Label).string = `${result[i].jackPotNum}`;
            // Set win amount
            itemRow.children[4].getComponent(Label).string = Utils.formatNumber(result[i].paylinePrizeValue);
        }
    }
}