import { BaseSlotSymbol, SymbolState } from "../../../Lobby/Slot/BaseSlotSymbol";
import KingdomController from "../KingdomController";

const {ccclass, property} = cc._decorator;

@ccclass("KingdomSlotSymbol")
export default class KingdomSlotSymbol extends BaseSlotSymbol {

    @property([cc.SpriteAtlas])
    private atlasSet: cc.SpriteAtlas[] = [];

    protected onLoad() {
        KingdomController.getInstance().addObserver(this.node.uuid, {
            onChangeRoom: (sender: KingdomController, roomID: number) => {
                this.onRoomChange(roomID);
            }
        });

        this.setStateHandler({
            [SymbolState.INIT]: async () => {
                this.defaultAnimation.node.active = false;
                this.symbol.node.color = cc.Color.BLACK.fromHEX('#ffffff');
                return Promise.resolve();
            },
            [SymbolState.HIDE]: async () => {
                this.defaultAnimation.node.active = false;
                this.symbol.node.color = cc.Color.BLACK.fromHEX('#4c4c4c');
                return Promise.resolve();
            },
            [SymbolState.HIGHLIGHT]: async () => {
                this.defaultAnimation.node.active = true;
                this.defaultAnimation.play("spark");
                return new Promise((resolve) => {
                    this.defaultAnimation.once(cc.Animation.EventType.FINISHED, () => {
                        resolve();
                    });
                });
            }
        });
    }

    private onRoomChange(roomID: number): void {
        switch (roomID) {
            case 1:
                this.defaultAtlas = this.atlasSet[0];
                break;
            case 2:
                this.defaultAtlas = this.atlasSet[1];
                break;
            case 3:
                this.defaultAtlas = this.atlasSet[2];
                break;
        }
        super.show();
    }

    protected onDestroy(): void {
        KingdomController.getInstance().removeObserver(this.node.uuid);
    }

}
