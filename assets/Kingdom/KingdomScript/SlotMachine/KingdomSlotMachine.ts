import { _decorator, Node, randomRangeInt } from 'cc';
import { KingdomAudioManager, AUDIO_CLIP } from "../KingdomAudioManager";
import KingdomPayLines from "./KingdomPayLines";
import KingdomSlotSymbol from "./KingdomSlotSymbol";
import { BaseSlotMachine } from "db://assets/Lobby/scripts/common/slot/BaseSlotMachine";

const { ccclass, property } = _decorator;


const TURBO_CONFIG = {
    elasticPercent: 30,
    symbolOffset: 24,
    spinDuration: 1.5,
    delayReel: 0.1,
    rowCount: 3,
};

const NORMAL_CONFIG = {
    elasticPercent: 40,
    symbolOffset: 24,
    spinDuration: 2,
    delayReel: 0.3,
    rowCount: 3,
};

@ccclass("KingdomSlotMachine")
export default class KingdomSlotMachine extends BaseSlotMachine {

    @property(KingdomPayLines)
    private payLines: KingdomPayLines = null;

    onLoad(){
        this.listeners = {
            onReelCompleted: () => {
                KingdomAudioManager.Instance.playEffect(AUDIO_CLIP.END_REEL_SPIN);
            }
        }
    }

    protected initializeSymbol(symbolNode: Node, isBlur: boolean): void {
        let sID = `${randomRangeInt(1, 8)}`
        symbolNode.getComponent(KingdomSlotSymbol).setIsBlur(isBlur).setId(sID).show();
    }

    override async highlightItems(indexes: number[], prizeLines?: string[]): Promise<void> {
        await this.payLines.showPayLinesAnimation(prizeLines);
        await super.highlightItems(indexes, prizeLines);
    }

    override async resetAllItems(): Promise<void> {
        super.resetAllItems();
        this.payLines.resetAllPayLines();
    }

    public setTurbo(isTurbo: boolean){
        this.setConfig(isTurbo ? TURBO_CONFIG : NORMAL_CONFIG);
    }

}

// @lass("KingdomSlotMachine")
// export default class KingdomSlotMachine extends Component {
//     @property([Node])
//     private reels: Node[] = [];

//     @property(Node)
//     private symbolTemplate: Node = null;

//     @property(KingdomPayLines)
//     private payLines: KingdomPayLines = null;

//     private spinConfig: SpinConfig;
//     private itemHeight: number;
//     private itemPosition: Node[] = [];

//     public setConfig(spinConfig: SpinConfig) {
//         this.spinConfig = spinConfig;
//         this.itemHeight = 232.7;
//     }

//     public initializeReels() {
//         this.reels.forEach((reel: Node) => reel.removeAllChildren());
//         for (let i = 0; i < this.reels.length; i++) {
//             const reel = this.reels[i];
//             for (let j = 0; j < this.spinConfig.rowCount * 2 + this.spinConfig.symbolOffset; j++) {
//                 const item = instantiate(this.symbolTemplate);
//                 item.active = true;
//                 reel.addChild(item);
//                 item.setPosition(0, this.itemHeight / 2 + this.itemHeight * j)
//                 item.getComponent(KingdomSlotSymbol).setIsBlur(j > 2).setID(`Item${Utils.randomRangeInt(1, 8)}`);
//             }
//         }
//         this.symbolTemplate.removeFromParent();
//         this.symbolTemplate = null;

//         const rowCount = this.spinConfig.rowCount;
//         const colCount = this.reels.length;

//         for (let row = rowCount - 1; row >= 0; row--) {
//             for (let col = 0; col < colCount; col++) {
//                 this.itemPosition.push(this.reels[col].children[row]);
//             }
//         }
//     }

//     public async startSpin(resultSpin: string): Promise<void> {
//         let matrix = resultSpin.split(",");
//         const { spinDuration, symbolOffset, delayReel, rowCount, elasticPercent } = this.spinConfig;
//         let distance = this.itemHeight * (rowCount + symbolOffset + elasticPercent / 100);
//         let reelCount = this.reels.length;
//         let spinPromises: Promise<void>[] = [];

//         for (let i = 0; i < reelCount; i++) {
//             let column = this.reels[i];
//             let items = column.children;

//             for (let j = 0; j < rowCount; j++) {
//                 let id = parseInt(matrix[i + j * reelCount]);
//                 items[column.childrenCount - 1 - j].getComponent(KingdomSlotSymbol).setIsBlur(false).setID(`Item${id}`);
//             }

//             // Create a promise for each reel spin
//             let spinPromise = new Promise<void>((resolve) => {
//                 tween(column)
//                     .delay(i * delayReel)
//                     .to(spinDuration, { y: -distance }, { easing: "quadOut" })
//                     .by(0.2, { y: this.itemHeight * elasticPercent / 100 }, { easing: "quadInOut" })
//                     .call(() => {
//                         for (let j = 0; j < rowCount; j++) {
//                             let id = parseInt(matrix[i + j * reelCount]);
//                             items[this.spinConfig.rowCount - 1 - j].getComponent(KingdomSlotSymbol).setIsBlur(false).setID(`Item${id}`);
//                         }
//                         column.y = 0;
//                         resolve();
//                     })
//                     .start();
//             });

//             spinPromises.push(spinPromise);
//         }

//         await Promise.all(spinPromises);
//     }

//     public getItemAtPosition(index: number): Node {
//         return this.itemPosition[index - 1];
//     }

//     public async highlightItems(indexes: number[], prizeLines: string[]) {
//         await this.payLines.showPayLinesAnimation(prizeLines);

//         await Promise.all(
//             indexes.map(async (i) => {
//                 let item = this.getItemAtPosition(i);
//                 await item.getComponent(KingdomSlotSymbol).setState(SymbolState.HIGHLIGHT);
//             })
//         );
//         this.resetAllItems();
//     }

//     public resetAllItems() {
//         for (let item of this.itemPosition) {
//             item.getComponent(KingdomSlotSymbol).setState(SymbolState.INIT).then();
//         }
//         this.payLines.resetAllPayLines();
//     }
// }
