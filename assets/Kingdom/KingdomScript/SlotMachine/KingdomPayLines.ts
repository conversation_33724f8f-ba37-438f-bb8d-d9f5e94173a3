const {ccclass, property} = cc._decorator;

@ccclass("KingdomPayLines")
export default class KingdomPayLines extends cc.Component {

    protected onLoad() {
        this.node.children.forEach(child => child.active = false);
    }

    public async showPayLinesAnimation(prizeLines: string[]): Promise<void> {
        let activePayLines: cc.Node[] = [];
        for (let p of prizeLines) {
            if(p !== '') {
                activePayLines.push(this.node.getChildByName(`Line${p.split(",")[0]}`));
            }
        }
        activePayLines.forEach(line => {
            line.active = true;
        });
    }

    public resetAllPayLines() {
        for (let line of this.node.children) {
            line.active = false;
        }
    }
}
