import App from "../../Lobby/LobbyScript/Script/common/App";
import Dialog from "../../Lobby/LobbyScript/Script/common/Dialog";
import Tween from "../../Lobby/LobbyScript/Script/common/Tween";
import Utils from "../../Lobby/LobbyScript/Script/common/Utils";

const {ccclass, property, menu} = cc._decorator;

@ccclass
@menu("Kingdom/Toast")
export default class Toast extends Dialog {
    @property(cc.Label)
    labelValue: cc.Label = null;

    @property(cc.Label)
    text1: cc.Label = null;

    @property(cc.Label)
    text2: cc.Label = null;

    private _resolve: () => void = null;

    private showToast(reward: number): Promise<void> {
        return new Promise<void>((resolve) => {
            this._resolve = resolve;
            this.labelValue.string = '';
            Tween.numberTo(this.labelValue, reward, 0.3);
            super.show();
        });
    }

    public showBonusReward(reward: number) {
        this.text1.string = App.instance.getTextLang("sl52"); // You won
        this.text2.string = App.instance.getTextLang("sl88"); // In Bonus game
        return this.showToast(reward);
    }

    public showFreeSpinReward(reward: number) {
        this.text1.string = App.instance.getTextLang("sl52"); // You won
        this.text2.string = App.instance.getTextLang("sl89");
        return this.showToast(reward);
    }

    public dismiss() {
        super.dismiss();
    }

    _onShowed() {
        super._onShowed();
        this.scheduleOnce(() => {
            this.dismiss();
        }, 2);
    }

    _onDismissed() {
        super._onDismissed();
        if (this._resolve) {
            this._resolve();
            this._resolve = null;
        }
    }
}
